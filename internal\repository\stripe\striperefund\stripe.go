package striperefund

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/client"
	"github.com/stripe/stripe-go/v72/refund"
)

type repository struct {
	client *refund.Client
}

func New(api *client.API) Repository {
	return &repository{
		client: api.Refunds,
	}
}

func (r *repository) Create(charge *stripe.Charge, reason string) (*stripe.Refund, error) {
	refund, err := r.client.New(&stripe.RefundParams{
		Charge: stripe.String(charge.ID),
		Reason: stripe.String(reason),
	})
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to create refund")
	}
	return refund, nil
}

func (r *repository) Find(id string) (*stripe.Refund, error) {
	refund, err := r.client.Get(id, nil)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to get refund")
	}
	return refund, nil
}

func (r *repository) FindAll() ([]*stripe.Refund, error) {
	refunds := make([]*stripe.Refund, 0)
	params := &stripe.RefundListParams{}
	params.Filters.AddFilter("limit", "", "100")

	i := r.client.List(params)
	for i.Next() {
		refunds = append(refunds, i.Refund())
	}

	if err := i.Err(); err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to list refunds")
	}

	return refunds, nil
}

func (r *repository) FindByCharge(charge string) (*stripe.Refund, error) {
	params := &stripe.RefundListParams{
		Charge: stripe.String(charge),
	}
	params.Filters.AddFilter("limit", "", "1")

	i := r.client.List(params)
	if i.Err() != nil {
		return nil, errors.HandleStripeError(errors.Repository, i.Err(), "failed to list refunds by charge")
	}
	for i.Next() {
		if err := i.Err(); err != nil {
			return nil, errors.HandleStripeError(errors.Repository, err, "failed to list refunds by charge")
		}
		return i.Refund(), nil
	}

	return nil, errors.New(errors.Repository, "refund not found by charge", errors.NotFound, nil)
}

func (r *repository) Update(id string, charge *stripe.Charge) (*stripe.Refund, error) {
	refund, err := r.client.Update(id, &stripe.RefundParams{
		Charge: stripe.String(charge.ID),
		Reason: stripe.String(string(stripe.RefundReasonRequestedByCustomer)),
	})
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to update refund")
	}
	return refund, nil
}

func (r *repository) Delete(id string) error {
	_, err := r.client.Cancel(id, nil)
	if err != nil {
		return errors.HandleStripeError(errors.Repository, err, "failed to delete refund")
	}
	return nil
}
