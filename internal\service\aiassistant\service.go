package aiassistant

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/aiassistant"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trophy"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
)

// Service defines the interface for the AI Assistant service
type Service interface {
	// FetchForUser retrieves all relevant user data for AI consumption
	FetchForUser(ctx context.Context, userID string) (*aiassistant.AIContextDTO, error)
}

// service implements the Service interface
type service struct {
	UserService           user.Service
	DreamboardService     dreamboard.Service
	FinancialDNAService   financialdna.Service
	FinancialSheetService financialsheet.Service
	ProgressionService    progression.Service
	VaultService          vault.Service
	ContractService       contract.Service
	WalletService         wallet.Service
	TrophyService         trophy.Service
	TrailService          trail.Service
}

// New creates a new instance of the AI Assistant service
func New(
	userService user.Service,
	dreamboardService dreamboard.Service,
	financialDNAService financialdna.Service,
	financialSheetService financialsheet.Service,
	progressionService progression.Service,
	vaultService vault.Service,
	contractService contract.Service,
	walletService wallet.Service,
	trophyService trophy.Service,
	trailService trail.Service,
) Service {
	return &service{
		UserService:           userService,
		DreamboardService:     dreamboardService,
		FinancialDNAService:   financialDNAService,
		FinancialSheetService: financialSheetService,
		ProgressionService:    progressionService,
		VaultService:          vaultService,
		ContractService:       contractService,
		WalletService:         walletService,
		TrophyService:         trophyService,
		TrailService:          trailService,
	}
}

// FetchForUser retrieves all relevant user data for AI consumption
func (s *service) FetchForUser(ctx context.Context, userID string) (*aiassistant.AIContextDTO, error) {
	// Initialize the result DTO
	result := &aiassistant.AIContextDTO{
		UserID:           userID,
		DataCompleteness: make(map[string]bool),
	}

	// Fetch user profile data
	profileData, err := s.fetchProfileData(ctx, userID)
	if err != nil {
		// Log error but continue with other data
		result.DataCompleteness["profile"] = false
	} else {
		result.ProfileSummary = profileData
		result.DataCompleteness["profile"] = true
	}

	// Fetch DSOP progress data
	dsopData, err := s.fetchDSOPProgressData(ctx, userID)
	if err != nil {
		// Log error but continue with other data
		result.DataCompleteness["dsop_progress"] = false
	} else {
		result.DSOPProgress = dsopData
		result.DataCompleteness["dsop_progress"] = true
	}

	// Fetch active dreams data
	dreamsData, err := s.fetchActiveDreamsData(ctx, userID)
	if err != nil {
		// Log error but continue with other data
		result.DataCompleteness["active_dreams"] = false
	} else {
		result.ActiveDreams = dreamsData
		result.DataCompleteness["active_dreams"] = true
	}

	// Fetch financial DNA data
	financialDNAData, err := s.fetchFinancialDNAData(ctx, userID)
	if err != nil {
		// Log error but continue with other data
		result.DataCompleteness["financial_dna"] = false
	} else {
		result.FinancialDNASnapshot = financialDNAData
		result.DataCompleteness["financial_dna"] = true
	}

	// Fetch financial habits data
	financialHabitsData, err := s.fetchFinancialHabitsData(ctx, userID)
	if err != nil {
		// Log error but continue with other data
		result.DataCompleteness["financial_habits"] = false
	} else {
		result.FinancialHabitsSummary = financialHabitsData
		result.DataCompleteness["financial_habits"] = true
	}

	// Fetch simulation highlights data
	simulationData, err := s.fetchSimulationHighlightsData(ctx, userID)
	if err != nil {
		// Log error but continue with other data
		result.DataCompleteness["simulation_highlights"] = false
	} else {
		result.SimulationHighlights = simulationData
		result.DataCompleteness["simulation_highlights"] = true
	}

	return result, nil
}
