package stripeproduct

import (
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/stripe/stripe-go/v72"
)

type Reader interface {
	FindAll() ([]*stripe.Product, error)
	Find(id string) (*stripe.Product, error)
}

type Writer interface {
	Create(product *billing.Product) (*stripe.Product, error)
	Update(id string, product *billing.Product) (*stripe.Product, error)
	Delete(id string) error
}

type Repository interface {
	Reader
	Writer
}
