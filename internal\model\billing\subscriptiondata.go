package billing

import "github.com/dsoplabs/dinbora-backend/internal/model"

type SubscriptionData struct {
	Customer         *model.User `json:"customer" bson:"customer"`
	Plan             string      `json:"plan,omitempty" bson:"plan"`
	Invoice          *Invoice    `json:"invoice" bson:"invoice"`
	AutomaticRenewal bool        `json:"automaticRenewal,omitempty" bson:"automaticRenewal"`
	Status           Status      `json:"status" bson:"status"`
	ExternalCode     string      `json:"externalCode" bson:"externalCode"`
}

func (s *SubscriptionData) Validate() error {

	if s.Customer == nil {
		return ErrSubscriptionRequiredCustomer
	}

	return nil
}
