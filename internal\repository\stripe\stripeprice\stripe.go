package stripeprice

import (
	"math"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/client"
	"github.com/stripe/stripe-go/v72/price"
)

type repository struct {
	client *price.Client
}

func New(api *client.API) Repository {
	return &repository{
		client: api.Prices,
	}
}

func (r *repository) Create(product *billing.Product, createProduct bool) (*stripe.Price, error) {
	params := &stripe.PriceParams{
		Currency: stripe.String(string(stripe.CurrencyBRL)),
		Recurring: &stripe.PriceRecurringParams{
			Interval: stripe.String("month"),
		},
		UnitAmount: stripe.Int64(int64(math.Floor(product.Pricing.Value * 100))),
		LookupKey:  stripe.String(product.Identifier),
	}

	if createProduct {
		params.ProductData = &stripe.PriceProductDataParams{
			Active:              stripe.Bool(true),
			ID:                  stripe.String(product.ID),
			Name:                stripe.String(product.Name),
			StatementDescriptor: stripe.String(product.Name),
		}
	} else {
		params.Product = stripe.String(product.ID)
	}

	price, err := r.client.New(params)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to create price")
	}
	return price, nil
}

func (r *repository) Find(id string) (*stripe.Price, error) {
	price, err := r.client.Get(id, nil)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to find price")
	}
	return price, nil
}

func (r *repository) FindAll() ([]*stripe.Price, error) {
	prices := make([]*stripe.Price, 0)
	params := &stripe.PriceListParams{}
	params.Filters.AddFilter("limit", "", "3")

	i := r.client.List(params)
	for i.Next() {
		prices = append(prices, i.Price())
	}
	if err := i.Err(); err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to list prices")
	}
	return prices, nil
}

func (r *repository) FindByLookUpKey(key string) (*stripe.Price, error) {
	prices := make([]*stripe.Price, 0)
	params := &stripe.PriceListParams{
		LookupKeys: stripe.StringSlice([]string{key}),
	}
	params.Filters.AddFilter("limit", "", "1")

	i := r.client.List(params)
	for i.Next() {
		prices = append(prices, i.Price())
	}
	if err := i.Err(); err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to find price by lookup key")
	}
	if len(prices) > 0 {
		return prices[0], nil
	}
	return nil, errors.New(errors.Repository, "price not found", errors.NotFound, nil)
}

func (r *repository) Update(id string, product *billing.Product) (*stripe.Price, error) {
	price, err := r.client.Update(id, &stripe.PriceParams{
		LookupKey: stripe.String(product.Identifier),
	})
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to update price")
	}
	return price, nil
}

func (r *repository) Delete(id string) error {
	return errors.New(errors.Repository, "delete not implemented for pricing", errors.Internal, nil)
}

func (r *repository) RemoveLookupKey(id string) error {
	_, err := r.client.Update(id, &stripe.PriceParams{LookupKey: stripe.String("")})
	if err != nil {
		return errors.HandleStripeError(errors.Repository, err, "failed to remove lookup key")
	}
	return nil
}
