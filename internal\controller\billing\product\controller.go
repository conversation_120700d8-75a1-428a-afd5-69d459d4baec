package product

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/product"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Find() echo.HandlerFunc
	FindByIdentifier() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc

	// Utility
	Sync() echo.HandlerFunc
}

type controller struct {
	Service product.Service
}

func New(service product.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (pc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	productGroup := legacyGroup.Group("billing/product/", middlewares.AuthGuard())

	// CRUD
	productGroup.POST("", pc.Create(), middlewares.AdminGuard())
	productGroup.GET(":id/", pc.Find())
	productGroup.POST("findByIdentifier/", pc.FindByIdentifier())
	productGroup.GET("findAll/", pc.FindAll())
	productGroup.PUT(":id/", pc.Update(), middlewares.AdminGuard())
	productGroup.DELETE(":id/", pc.Delete(), middlewares.AdminGuard())

	// Utility
	productGroup.POST("sync/", pc.Sync(), middlewares.AdminGuard())
}

// CRUD
func (pc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var product billing.Product
		if err := c.Bind(&product); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := product.PrepareCreate(); err != nil {
			return err
		}

		if err := pc.Service.Create(ctx, &product); err != nil {
			return err
		}

		createdProduct, err := pc.Service.FindByIdentifier(ctx, product.Identifier)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, createdProduct.Sanitize())
	}
}

func (pc controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		product, err := pc.Service.Find(ctx, c.Param("id"))

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, product.Sanitize())
	}
}

func (pc *controller) FindByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		identifierParam := strings.ToLower(strings.TrimSpace(body["identifier"].(string)))
		product, err := pc.Service.FindByIdentifier(ctx, identifierParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, product.Sanitize())
	}
}

func (pc *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := pc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, product := range result {
			product.Sanitize()
		}

		return c.JSON(http.StatusOK, result)
	}
}

func (pc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		product, err := pc.Service.Find(ctx, sanitizeString(c.Param("id")))
		if err != nil {
			return err
		}

		var newProduct billing.Product
		if err = c.Bind(&newProduct); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = product.PrepareUpdate(&newProduct); err != nil {
			return err
		}

		if err = pc.Service.Update(ctx, product); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, product.Sanitize())
	}
}

func (pc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		if err := pc.Service.Delete(ctx, sanitizeString(c.Param("id"))); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Utility
func (pc controller) Sync() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		if err := pc.Service.SyncAll(ctx); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Helper
func sanitizeString(str string) string {
	return strings.ToLower(strings.TrimSpace(str))
}
