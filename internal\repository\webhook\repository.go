package webhook

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
)

type Reader interface {
	Find(ctx context.Context, id string) (*model.<PERSON>boraWebhook, error)
	FindByUrl(ctx context.Context, url string) (*model.<PERSON>boraWebhook, error)
}

type Writer interface {
	Create(ctx context.Context, webhook *model.DinboraWebhook) error
	Update(ctx context.Context, webhook *model.DinboraWebhook) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
