package content

import (
	"strings"
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Trail struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID       string             `json:"_id,omitempty" bson:"-"`

	AccessControl *AccessControl `json:"accessControl,omitempty" bson:"accessControl,omitempty"`
	CompanyLogo   string         `json:"companyLogo,omitempty" bson:"companyLogo,omitempty"`
	ThemeColor    string         `json:"themeColor,omitempty" bson:"themeColor,omitempty"`

	Name         string     `json:"name" bson:"name"`
	Identifier   string     `json:"identifier" bson:"identifier"`
	Level        uint8      `json:"level" bson:"level"`
	Logo         string     `json:"logo" bson:"logo"`
	Color        string     `json:"color" bson:"color"`
	Lessons      []*Lesson  `json:"lessons" bson:"lessons"`
	Requirements []string   `json:"requirements" bson:"requirements"`
	Challenge    *Challenge `json:"challenge" bson:"challenge"`
	CreatedAt    time.Time  `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt" bson:"updatedAt"`
}

// TrailCard is a lightweight struct containing only the fields needed for trail cards
type TrailCard struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID       string             `json:"_id,omitempty" bson:"-"`

	AccessControl *AccessControl `json:"-" bson:"accessControl,omitempty"`
	CompanyLogo   string         `json:"companyLogo,omitempty" bson:"companyLogo,omitempty"`
	ThemeColor    string         `json:"themeColor,omitempty" bson:"themeColor,omitempty"`

	Name         string   `json:"name" bson:"name"`
	Identifier   string   `json:"identifier" bson:"identifier"`
	Level        uint8    `json:"level" bson:"level"`
	Logo         string   `json:"logo" bson:"logo"`
	Color        string   `json:"color" bson:"color"`
	Requirements []string `json:"requirements" bson:"requirements"`
	//LessonCount  int                `json:"lessonCount" bson:"-"` // Derived field for progression calculation
}

type Tutorial struct {
	ObjectID     primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID           string             `json:"_id,omitempty" bson:"-"`
	Name         string             `json:"name" bson:"name"`
	Identifier   string             `json:"identifier" bson:"identifier"`
	Ticker       string             `json:"ticker" bson:"ticker"`
	Level        uint8              `json:"level" bson:"level"`
	Logo         string             `json:"logo" bson:"logo"`
	Color        string             `json:"color" bson:"color"`
	Lessons      []*Lesson          `json:"lessons" bson:"lessons"`
	Requirements []string           `json:"requirements" bson:"requirements"`
	Challenge    *Challenge         `json:"challenge" bson:"challenge"`
	CreatedAt    time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt    time.Time          `json:"updatedAt" bson:"updatedAt"`
}

func (t *Trail) Sanitize() *Trail {
	t.ID = t.ObjectID.Hex()

	return t
}

func (t *Tutorial) Sanitize() *Tutorial {
	t.ID = t.ObjectID.Hex()

	return t
}

func (t *Trail) GetLesson(identifier string) *Lesson {
	for _, lesson := range t.Lessons {
		if lesson.Identifier == identifier {
			return lesson
		}
	}

	return nil
}
func (t *Trail) PrepareCreate() error {
	t.Name = strings.TrimSpace(t.Name)
	t.Identifier = strings.TrimSpace(t.Identifier)
	t.CompanyLogo = strings.TrimSpace(t.CompanyLogo)
	t.ThemeColor = strings.TrimSpace(t.ThemeColor)

	t.CreatedAt = time.Now()
	t.UpdatedAt = t.CreatedAt

	for _, lesson := range t.Lessons {
		if err := lesson.PrepareCreate(); err != nil {
			return err
		}
	}

	// Validate access control if present
	if t.AccessControl != nil {
		if err := t.AccessControl.PrepareCreate(); err != nil {
			return err
		}
	}

	if err := t.ValidateCreate(); err != nil {
		return err
	}

	if t.Challenge != nil {
		if err := t.Challenge.PrepareCreate(); err != nil {
			return err
		}
	}

	return nil
}

func (t *Tutorial) PrepareCreate() error {
	t.Name = strings.TrimSpace(t.Name)
	t.Identifier = strings.TrimSpace(t.Identifier)

	t.CreatedAt = time.Now()
	t.UpdatedAt = t.CreatedAt

	for _, lesson := range t.Lessons {
		if err := lesson.PrepareCreate(); err != nil {
			return err
		}
	}

	if err := t.ValidateCreate(); err != nil {
		return err
	}

	if t.Challenge != nil {
		if err := t.Challenge.PrepareCreate(); err != nil {
			return err
		}
	}

	return nil
}

func (t *Trail) ValidateCreate() error {
	if t.Name == "" {
		return ErrTrailRequiredName
	}

	if t.Identifier == "" {
		return ErrTrailRequiredIdentifier
	}

	if t.Logo == "" {
		return ErrTrailRequiredLogo
	}

	if t.Lessons == nil {
		return ErrTrailRequiredLessons
	}

	return nil
}

func (t *Tutorial) ValidateCreate() error {
	if t.Name == "" {
		return ErrTrailRequiredName
	}

	if t.Identifier == "" {
		return ErrTrailRequiredIdentifier
	}

	if t.Logo == "" {
		return ErrTrailRequiredLogo
	}

	if t.Ticker == "" {
		return ErrTrailRequiredTicker
	}

	if t.Lessons == nil {
		return ErrTrailRequiredLessons
	}

	return nil
}

/////////////////// UPDATE

func (t *Trail) PrepareUpdate(newTrail *Trail) error {

	if err := mergo.Merge(t, newTrail, mergo.WithOverride); err != nil {
		return err
	}

	t.UpdatedAt = time.Now()

	if err := t.ValidateUpdate(); err != nil {
		return err
	}

	for _, lesson := range t.Lessons {
		if err := lesson.PrepareUpdate(); err != nil {
			return err
		}
	}

	if t.Challenge != nil {
		if err := t.Challenge.PrepareUpdate(); err != nil {
			return err
		}
	}

	return nil
}

func (t *Tutorial) PrepareUpdate(newTutorial *Tutorial) error {

	if err := mergo.Merge(t, newTutorial, mergo.WithOverride); err != nil {
		return err
	}

	t.UpdatedAt = time.Now()

	if err := t.ValidateUpdate(); err != nil {
		return err
	}

	for _, lesson := range t.Lessons {
		if err := lesson.PrepareUpdate(); err != nil {
			return err
		}
	}

	if t.Challenge != nil {
		if err := t.Challenge.PrepareUpdate(); err != nil {
			return err
		}
	}

	return nil
}

func (t *Trail) ValidateUpdate() error {

	if t.ID == "" {
		if !t.ObjectID.IsZero() {
			t.ID = t.ObjectID.Hex()
		} else {
			return ErrTrailInvalidID
		}
	} else {
		trailObjectID, err := primitive.ObjectIDFromHex(t.ID)
		if err != nil {
			return err
		}
		t.ObjectID = trailObjectID
	}

	return t.ValidateCreate()
}

func (t *Tutorial) ValidateUpdate() error {

	if t.ID == "" {
		if !t.ObjectID.IsZero() {
			t.ID = t.ObjectID.Hex()
		} else {
			return ErrTutorialInvalidID
		}
	} else {
		tutorialObjectID, err := primitive.ObjectIDFromHex(t.ID)
		if err != nil {
			return err
		}
		t.ObjectID = tutorialObjectID
	}

	return t.ValidateCreate()
}

// Access Control Methods

// HasAccess checks if a user has access to this trail based on their contracts and classification
func (t *Trail) HasAccess(userClassification string, userContractIDs []string) bool {
	// If no access control is set, allow access (backward compatibility)
	if t.AccessControl == nil {
		return true
	}
	return t.AccessControl.HasAccess(userClassification, userContractIDs)
}

// IsAccessActive checks if the access control for this trail is currently active
func (t *Trail) IsAccessActive() bool {
	// If no access control is set, consider it always active (backward compatibility)
	if t.AccessControl == nil {
		return true
	}
	return t.AccessControl.IsActive()
}

// HasAccess checks if a user has access to this trail card based on their contracts and classification
func (t *TrailCard) HasAccess(userClassification string, userContractIDs []string) bool {
	// If no access control is set, allow access (backward compatibility)
	if t.AccessControl == nil {
		return true
	}
	return t.AccessControl.HasAccess(userClassification, userContractIDs)
}

// IsAccessActive checks if the access control for this trail card is currently active
func (t *TrailCard) IsAccessActive() bool {
	// If no access control is set, consider it always active (backward compatibility)
	if t.AccessControl == nil {
		return true
	}
	return t.AccessControl.IsActive()
}
