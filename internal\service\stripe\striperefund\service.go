package striperefund

import (
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/striperefund"
	"github.com/stripe/stripe-go/v72"
)

type Service interface {
	// Stripe Fund CRUD
	Create(charge *stripe.Charge, reason string) (*stripe.Refund, error)
	Find(id string) (*stripe.Refund, error)
	FindAll() ([]*stripe.Refund, error)
	Update(id string, charge *stripe.Charge) (*stripe.Refund, error)
	Delete(id string) error

	// Utility
	FindByCharge(charge string) (*stripe.Refund, error)
}

type service struct {
	Repository striperefund.Repository
}

func New(repository striperefund.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(charge *stripe.Charge, reason string) (*stripe.Refund, error) {
	return s.Repository.Create(charge, reason)
}

func (s *service) Find(id string) (*stripe.Refund, error) {
	return s.Repository.Find(id)
}

func (s *service) FindAll() ([]*stripe.Refund, error) {
	return s.Repository.FindAll()
}

func (s *service) Update(id string, charge *stripe.Charge) (*stripe.Refund, error) {
	return s.Repository.Update(id, charge)
}

func (s *service) Delete(id string) error {
	return s.Repository.Delete(id)
}

// Utility
func (s *service) FindByCharge(charge string) (*stripe.Refund, error) {
	return s.Repository.FindByCharge(charge)
}
