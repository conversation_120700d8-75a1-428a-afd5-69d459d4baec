package trail

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (m mongoDB) CreateExtraTrail(ctx context.Context, content *content.Trail) error {
	_, err := m.extraCollection.InsertOne(ctx, content)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "extra trail already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create extra trail", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) FindExtraTrail(ctx context.Context, id string, userClassification string, contractIDs []string) (*content.Trail, error) {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	// Create a filter for access control
	now := time.Now()

	// Build the query to find trails that:
	// 1. Match the trail ID
	// 2. Have access control and valid date range and permissions
	filter := bson.D{
		{Key: "_id", Value: trailObjectID},
		{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: true}, {Key: "$ne", Value: nil}}},
		{Key: "$or", Value: []bson.D{
			// No date restrictions
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validFrom exists and is in the past
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validUntil exists and is in the future
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
			// Both exist and form a valid range
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
		}},
	}

	// Add access control filter for user permissions
	if len(contractIDs) > 0 || userClassification != "" {
		accessConditions := []bson.D{}

		// If we have contract IDs, add them to the filter
		if len(contractIDs) > 0 {
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: content.AccessTypeCorporate},
				{Key: "accessControl.contractIds", Value: bson.D{{Key: "$in", Value: contractIDs}}},
			})
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: bson.D{
					{Key: "$in", Value: []content.AccessType{
						content.AccessTypeFree,
						content.AccessTypeSubscription,
						content.AccessTypePremium,
					}},
				}},
			})
		}

		// If we have a user classification, add it to the filter
		if userClassification != "" {
			accessConditions = append(accessConditions, bson.D{
				{Key: "$or", Value: []bson.D{
					// Either no classifications specified (open to all)
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$exists", Value: false}}}},
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$size", Value: 0}}}},
					// Or user's classification is in the allowed list
					{{Key: "accessControl.userClassifications", Value: userClassification}},
				}},
			})
		}

		if len(accessConditions) > 0 {
			accessFilter := bson.D{
				{Key: "$or", Value: accessConditions},
			}
			filter = bson.D{
				{Key: "$and", Value: []bson.D{filter, accessFilter}},
			}
		}
	}

	var trailContent content.Trail
	if err = m.extraCollection.FindOne(ctx, filter).Decode(&trailContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "extra trail not found or access denied", errors.Forbidden, err)
		}
		return nil, errors.New(errors.Repository, "failed to find extra trail", errors.Internal, err)
	}

	return &trailContent, nil
}

func (m mongoDB) FindAllExtraTrails(ctx context.Context, userClassification string, contractIDs []string) ([]*content.Trail, error) {
	// Create a filter for access control
	now := time.Now()

	// Filter where accessControl exists and is not null
	filter := bson.D{
		{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: true}, {Key: "$ne", Value: nil}}},
	}

	// Add date range validation
	dateFilter := bson.D{
		{Key: "$or", Value: []bson.D{
			// No date restrictions
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validFrom exists and is in the past
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validUntil exists and is in the future
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
			// Both exist and form a valid range
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
		}},
	}

	// Combine filters
	filter = bson.D{
		{Key: "$and", Value: []bson.D{filter, dateFilter}},
	}

	// Add access control filter for user permissions
	if len(contractIDs) > 0 || userClassification != "" {
		accessConditions := []bson.D{}

		// If we have contract IDs, add them to the filter
		if len(contractIDs) > 0 {
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: content.AccessTypeCorporate},
				{Key: "accessControl.contractIds", Value: bson.D{{Key: "$in", Value: contractIDs}}},
			})
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: bson.D{
					{Key: "$in", Value: []content.AccessType{
						content.AccessTypeFree,
						content.AccessTypeSubscription,
						content.AccessTypePremium,
					}},
				}},
			})
		}

		// If we have a user classification, add it to the filter
		if userClassification != "" {
			accessConditions = append(accessConditions, bson.D{
				{Key: "$or", Value: []bson.D{
					// Either no classifications specified (open to all)
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$exists", Value: false}}}},
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$size", Value: 0}}}},
					// Or user's classification is in the allowed list
					{{Key: "accessControl.userClassifications", Value: userClassification}},
				}},
			})
		}

		if len(accessConditions) > 0 {
			accessFilter := bson.D{
				{Key: "$or", Value: accessConditions},
			}
			filter = bson.D{
				{Key: "$and", Value: []bson.D{filter, accessFilter}},
			}
		}
	}

	cursor, err := m.extraCollection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query extra trails", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var trails []*content.Trail
	for cursor.Next(ctx) {
		var trail content.Trail
		if err = cursor.Decode(&trail); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode extra trail", errors.Internal, err)
		}
		trail.ID = trail.ObjectID.Hex()
		trails = append(trails, &trail)
	}

	return trails, nil
}

func (m mongoDB) FindExtraTrailByIdentifier(ctx context.Context, identifier string, userClassification string, contractIDs []string) (*content.Trail, error) {
	// Create a filter for access control
	now := time.Now()

	// Build the query to find trails that:
	// 1. Match the identifier
	// 2. Have access control and valid date range and permissions
	filter := bson.D{
		{Key: "identifier", Value: identifier},
		{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: true}, {Key: "$ne", Value: nil}}},
		{Key: "$or", Value: []bson.D{
			// No date restrictions
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validFrom exists and is in the past
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validUntil exists and is in the future
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
			// Both exist and form a valid range
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
		}},
	}

	// Add access control filter for user permissions
	if len(contractIDs) > 0 || userClassification != "" {
		accessConditions := []bson.D{}

		// If we have contract IDs, add them to the filter
		if len(contractIDs) > 0 {
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: content.AccessTypeCorporate},
				{Key: "accessControl.contractIds", Value: bson.D{{Key: "$in", Value: contractIDs}}},
			})
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: bson.D{
					{Key: "$in", Value: []content.AccessType{
						content.AccessTypeFree,
						content.AccessTypeSubscription,
						content.AccessTypePremium,
					}},
				}},
			})
		}

		// If we have a user classification, add it to the filter
		if userClassification != "" {
			accessConditions = append(accessConditions, bson.D{
				{Key: "$or", Value: []bson.D{
					// Either no classifications specified (open to all)
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$exists", Value: false}}}},
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$size", Value: 0}}}},
					// Or user's classification is in the allowed list
					{{Key: "accessControl.userClassifications", Value: userClassification}},
				}},
			})
		}

		if len(accessConditions) > 0 {
			accessFilter := bson.D{
				{Key: "$or", Value: accessConditions},
			}
			filter = bson.D{
				{Key: "$and", Value: []bson.D{filter, accessFilter}},
			}
		}
	}

	var trailContent content.Trail
	if err := m.extraCollection.FindOne(ctx, filter).Decode(&trailContent); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "extra trail not found or access denied", errors.Forbidden, err)
		}
		return nil, errors.New(errors.Repository, "failed to find extra trail by identifier", errors.Internal, err)
	}

	return &trailContent, nil
}

func (m mongoDB) UpdateExtraTrail(ctx context.Context, content *content.Trail) error {
	result, err := m.extraCollection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: content.ObjectID}},
		primitive.M{"$set": content})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "extra trail already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update extra trail", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "extra trail not found", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) DeleteExtraTrail(ctx context.Context, id string) error {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	result, err := m.extraCollection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trailObjectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete extra trail", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "extra trail not found", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) FindExtraTrailCardData(ctx context.Context, id string, userClassification string, contractIDs []string) (*content.TrailCard, error) {
	trailObjectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid trail ID format", errors.Validation, err)
	}

	// Create a filter for access control
	now := time.Now()

	// Build the query to find trails that:
	// 1. Match the trail ID
	// 2. Have access control and valid date range and permissions
	filter := bson.D{
		{Key: "_id", Value: trailObjectID},
		{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: true}, {Key: "$ne", Value: nil}}},
		{Key: "$or", Value: []bson.D{
			// No date restrictions
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validFrom exists and is in the past
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validUntil exists and is in the future
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
			// Both exist and form a valid range
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
		}},
	}

	// Add access control filter for user permissions
	if len(contractIDs) > 0 || userClassification != "" {
		accessConditions := []bson.D{}

		// If we have contract IDs, add them to the filter
		if len(contractIDs) > 0 {
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: content.AccessTypeCorporate},
				{Key: "accessControl.contractIds", Value: bson.D{{Key: "$in", Value: contractIDs}}},
			})
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: bson.D{
					{Key: "$in", Value: []content.AccessType{
						content.AccessTypeFree,
						content.AccessTypeSubscription,
						content.AccessTypePremium,
					}},
				}},
			})
		}

		// If we have a user classification, add it to the filter
		if userClassification != "" {
			accessConditions = append(accessConditions, bson.D{
				{Key: "$or", Value: []bson.D{
					// Either no classifications specified (open to all)
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$exists", Value: false}}}},
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$size", Value: 0}}}},
					// Or user's classification is in the allowed list
					{{Key: "accessControl.userClassifications", Value: userClassification}},
				}},
			})
		}

		if len(accessConditions) > 0 {
			accessFilter := bson.D{
				{Key: "$or", Value: accessConditions},
			}
			filter = bson.D{
				{Key: "$and", Value: []bson.D{filter, accessFilter}},
			}
		}
	}

	// Set options to optimize query performance
	findOptions := options.FindOne()

	// Add projection to include only necessary fields for card data
	projection := bson.D{
		{Key: "accessControl", Value: 1},
		{Key: "companyLogo", Value: 1},
		{Key: "themeColor", Value: 1},
		{Key: "name", Value: 1},
		{Key: "identifier", Value: 1},
		{Key: "level", Value: 1},
		{Key: "logo", Value: 1},
		{Key: "color", Value: 1},
		{Key: "requirements", Value: 1},
	}
	findOptions.SetProjection(projection)

	var cardData content.TrailCard
	if err = m.extraCollection.FindOne(ctx, filter, findOptions).Decode(&cardData); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "extra trail not found or access denied", errors.Forbidden, err)
		}
		return nil, errors.New(errors.Repository, "failed to find extra trail card data", errors.Internal, err)
	}

	cardData.ID = cardData.ObjectID.Hex()

	return &cardData, nil
}

func (m mongoDB) FindAllExtraTrailsCardData(ctx context.Context, userClassification string, contractIDs []string) ([]*content.TrailCard, error) {
	// Create a filter for access control
	now := time.Now()

	// Filter where accessControl exists and is not null
	filter := bson.D{
		{Key: "accessControl", Value: bson.D{{Key: "$exists", Value: true}, {Key: "$ne", Value: nil}}},
		{Key: "$or", Value: []bson.D{
			// No date restrictions
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validFrom exists and is in the past
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$exists", Value: false}}},
			},
			// Only validUntil exists and is in the future
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$exists", Value: false}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
			// Both exist and form a valid range
			{
				{Key: "accessControl.validFrom", Value: bson.D{{Key: "$lte", Value: now}}},
				{Key: "accessControl.validUntil", Value: bson.D{{Key: "$gte", Value: now}}},
			},
		}},
	}

	// Add access control filter for user permissions
	if len(contractIDs) > 0 || userClassification != "" {
		accessConditions := []bson.D{}

		// If we have contract IDs, add them to the filter
		if len(contractIDs) > 0 {
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: content.AccessTypeCorporate},
				{Key: "accessControl.contractIds", Value: bson.D{{Key: "$in", Value: contractIDs}}},
			})
			accessConditions = append(accessConditions, bson.D{
				{Key: "accessControl.type", Value: bson.D{
					{Key: "$in", Value: []content.AccessType{
						content.AccessTypeFree,
						content.AccessTypeSubscription,
						content.AccessTypePremium,
					}},
				}},
			})
		}

		// If we have a user classification, add it to the filter
		if userClassification != "" {
			accessConditions = append(accessConditions, bson.D{
				{Key: "$or", Value: []bson.D{
					// Either no classifications specified (open to all)
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$exists", Value: false}}}},
					{{Key: "accessControl.userClassifications", Value: bson.D{{Key: "$size", Value: 0}}}},
					// Or user's classification is in the allowed list
					{{Key: "accessControl.userClassifications", Value: userClassification}},
				}},
			})
		}

		if len(accessConditions) > 0 {
			accessFilter := bson.D{
				{Key: "$or", Value: accessConditions},
			}
			filter = bson.D{
				{Key: "$and", Value: []bson.D{filter, accessFilter}},
			}
		}
	}

	// Set options to optimize query performance
	findOptions := options.Find()

	// Add projection to include only necessary fields for card data
	projection := bson.D{
		{Key: "accessControl", Value: 1},
		{Key: "companyLogo", Value: 1},
		{Key: "themeColor", Value: 1},
		{Key: "name", Value: 1},
		{Key: "identifier", Value: 1},
		{Key: "level", Value: 1},
		{Key: "logo", Value: 1},
		{Key: "color", Value: 1},
		{Key: "requirements", Value: 1},
	}
	findOptions.SetProjection(projection)

	cursor, err := m.extraCollection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query extra trail cards", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var cardDataList []*content.TrailCard
	for cursor.Next(ctx) {
		var cardData content.TrailCard
		if err = cursor.Decode(&cardData); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode extra trail card data", errors.Internal, err)
		}

		cardData.ID = cardData.ObjectID.Hex()
		cardDataList = append(cardDataList, &cardData)
	}

	return cardDataList, nil
}
