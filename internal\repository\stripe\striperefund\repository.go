package striperefund

import "github.com/stripe/stripe-go/v72"

type Reader interface {
	Find(id string) (*stripe.Refund, error)
	FindAll() ([]*stripe.Refund, error)
	FindByCharge(charge string) (*stripe.Refund, error)
}

type Writer interface {
	Create(charge *stripe.Charge, reason string) (*stripe.Refund, error)
	Update(id string, charge *stripe.Charge) (*stripe.Refund, error)
	Delete(id string) error
}

type Repository interface {
	Reader
	Writer
}
