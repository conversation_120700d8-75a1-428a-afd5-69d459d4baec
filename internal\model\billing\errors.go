package billing

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

var (
	ErrProductInvalidID       = errors.OldError("product id is invalid", errors.NotFound, nil)
	ErrTypeRequiredName       = errors.OldError("product type name is a required field", errors.Validation, nil)
	ErrTypeRequiredIdentifier = errors.OldError("product type identifier is a required field", errors.Validation, nil)
)

var (
	ErrInstallmentInvalidValue = errors.OldError("product installment value must be a positive float number or zero", errors.Validation, nil)
	ErrInstallmentInvalidOrder = errors.OldError("product installment order must be a valid positive number", errors.Validation, nil)
)

var (
	ErrFeatureRequiredName       = errors.OldError("product feature name is a required field", errors.Validation, nil)
	ErrFeatureRequiredIdentifier = errors.OldError("product feature identifier is a required field", errors.Validation, nil)
)

var (
	ErrProductRequiredName       = errors.OldError("product name is a required field", errors.Validation, nil)
	ErrProductRequiredIdentifier = errors.OldError("product identifier is a required field", errors.Validation, nil)
	ErrProductRequiredType       = errors.OldError("product type is a required field", errors.Validation, nil)
	ErrProductRequiredFeatures   = errors.OldError("product features is a required field", errors.Validation, nil)
	ErrProductRequiredPricing    = errors.OldError("product pricing is a required field", errors.Validation, nil)
	ErrProductInvalidValue       = errors.OldError("product value must be a positive valid float number", errors.Validation, nil)
)

var (
	ErrContractRequiredCustomer              = errors.OldError("contract customer is a required field", errors.Validation, nil)
	ErrContractRequiredPaymentData           = errors.OldError("contract payment data is a required field", errors.Validation, nil)
	ErrContractRequiredPlan                  = errors.OldError("contract plan is a required field", errors.Validation, nil)
	ErrContractInvalidStatus                 = errors.OldError("invalid contract status", errors.Validation, nil)
	ErrPaymentDataRequiredPaymentDate        = errors.OldError("payment data payment date is a required field", errors.Validation, nil)
	ErrPaymentDataRequiredExpirationDate     = errors.OldError("payment data expiration date is a required field", errors.Validation, nil)
	ErrPaymentDateMustBeBeforeExpirationDate = errors.OldError("payment date must be before expiration date", errors.Validation, nil)
)

var (
	ErrSubscriptionInvalidPaymentMethod = errors.OldError("invalid payment method", errors.Validation, nil)
	ErrSubscriptionRequiredPlan         = errors.OldError("plan is a required field", errors.Validation, nil)
	ErrRequiredCardNumber               = errors.OldError("card number is a required field", errors.Validation, nil)
	ErrRequiredCardHolder               = errors.OldError("card holder is a required field", errors.Validation, nil)
	ErrRequiredCardExpDate              = errors.OldError("card expiration date is a required field", errors.Validation, nil)
	ErrSubscriptionRequiredCustomer     = errors.OldError("required customer", errors.Validation, nil)
)
