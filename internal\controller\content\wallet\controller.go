package wallet

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/wallet"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Find() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	FindByIdentifier() echo.HandlerFunc
	FindByUser() echo.HandlerFunc
	FindByRecommendation() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc
}

type controller struct {
	Service wallet.Service
}

func New(service wallet.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (tc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	walletContentGroup := legacyGroup.Group("content/wallet/")

	// CRUD
	walletContentGroup.POST("", tc.Create(), middlewares.AdminGuard())
	walletContentGroup.GET(":id/", tc.Find(), middlewares.AdminGuard())
	walletContentGroup.GET("findAll/", tc.FindAll(), middlewares.AdminGuard())
	walletContentGroup.POST("findByIdentifier/", tc.FindByIdentifier(), middlewares.AdminGuard())
	walletContentGroup.GET("getUserWallets/", tc.FindByUser(), middlewares.AuthGuard())
	walletContentGroup.POST("getUserWalletsRecommendation/", tc.FindByRecommendation(), middlewares.AuthGuard())
	walletContentGroup.PUT(":id/", tc.Update(), middlewares.AdminGuard())
	walletContentGroup.DELETE(":id/", tc.Delete(), middlewares.AdminGuard())
}

// CRUD
func (tc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var wallet content.Wallet
		if err := c.Bind(&wallet); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := wallet.PrepareCreate(); err != nil {
			return err
		}

		if err := tc.Service.Create(ctx, &wallet); err != nil {
			return err
		}

		createdWallet, err := tc.Service.FindByIdentifier(ctx, wallet.Identifier)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, createdWallet.Sanitize())
	}
}

func (tc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		wallet, err := tc.Service.Find(ctx, c.Param("id"))

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, wallet.Sanitize())
	}
}

func (tc *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := tc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, wallet := range result {
			wallet.Sanitize()
		}

		return c.JSON(http.StatusOK, result)
	}
}

func (tc *controller) FindByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		identifierParam := strings.ToLower(strings.TrimSpace(body["identifier"].(string)))
		wallet, err := tc.Service.FindByIdentifier(ctx, identifierParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, wallet.Sanitize())
	}
}

func (tc *controller) FindByUser() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		loggedUser, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		userWallets, err := tc.Service.FindByUser(ctx, loggedUser.Uid)
		if err != nil {
			return err
		}

		if userWallets == nil || len(userWallets) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, userWallets)
	}
}

func (tc *controller) FindByRecommendation() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		keys := make([]string, 0, len(body))
		for k := range body {
			keys = append(keys, k)
		}

		var userWallet string
		if len(keys) > 1 {
			userWallet = strings.ToLower(strings.TrimSpace(body["identifier"].(string)))
		} else {
			userWallet = "arkc-beginner"
		}
		category := strings.ToLower(strings.TrimSpace(body["category"].(string)))

		loggedUser, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		userWallets, err := tc.Service.FindByUser(ctx, loggedUser.Uid)
		if err != nil {
			return err
		}

		if userWallets == nil || len(userWallets) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		var userWalletRecommendation []*content.Recommendation = nil

		for _, wallet := range userWallets {
			if wallet.Identifier == userWallet {
				userWalletRecommendation, err = tc.Service.FindByRecommendation(ctx, wallet, category)
				if err != nil {
					return err
				}
			}
		}

		if userWalletRecommendation == nil {
			return errors.New(errors.Controller, "wallet not found", errors.NotFound, nil)
		}

		return c.JSON(http.StatusOK, userWalletRecommendation)
	}
}

func (tc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		wallet, err := tc.Service.Find(ctx, sanitizeString(c.Param("id")))
		if err != nil {
			return err
		}

		var newWallet content.Wallet
		if err = c.Bind(&newWallet); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = wallet.PrepareUpdate(&newWallet); err != nil {
			return err
		}

		if err = tc.Service.Update(ctx, wallet); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, wallet.Sanitize())
	}
}

func (tc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		if err := tc.Service.Delete(ctx, sanitizeString(c.Param("id"))); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Helper
func sanitizeString(str string) string {
	return strings.ToLower(strings.TrimSpace(str))
}
