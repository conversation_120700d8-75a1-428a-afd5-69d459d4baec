package stripeproduct

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripeproduct"
	"github.com/stripe/stripe-go/v72"
)

type Service interface {
	// Stripe Product CRUD
	Create(product *billing.Product) (*stripe.Product, error)
	Find(id string) (*stripe.Product, error)
	FindAll() ([]*stripe.Product, error)
	Update(id string, product *billing.Product) (*stripe.Product, error)
	Delete(id string) error
}

type service struct {
	Repository stripeproduct.Repository
}

func New(repository stripeproduct.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(product *billing.Product) (*stripe.Product, error) {
	foundProduct, err := s.Find(product.ID)
	if err != nil {
		if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) {
			return nil, err
		}
	}

	if foundProduct != nil {
		return foundProduct, nil
	}

	return s.Repository.Create(product)
}

func (s *service) Find(id string) (*stripe.Product, error) {
	return s.Repository.Find(id)
}

func (s *service) FindAll() ([]*stripe.Product, error) {
	return s.Repository.FindAll()
}

func (s *service) Update(id string, product *billing.Product) (*stripe.Product, error) {
	_, err := s.Find(id)
	if err != nil {
		return nil, err
	}

	return s.Repository.Update(id, product)
}

func (s *service) Delete(id string) error {
	return s.Repository.Delete(id)
}
