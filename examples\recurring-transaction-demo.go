package main

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/controller/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// This is a demonstration of how to use the recurring transaction feature
func main() {
	fmt.Println("=== Recurring Transaction Feature Demo ===\n")

	// Example 1: Monthly Salary
	fmt.Println("Example 1: Monthly Salary")
	salaryRequest := financialsheet.RecurringTransactionRequest{
		Category:         "compensation",
		MoneySource:      1, // Primary income source
		Value:            monetary.Amount(500000), // $5,000.00 in cents
		Date:             time.Date(2024, 3, 15, 10, 0, 0, 0, time.UTC),
		PaymentMethod:    2, // Bank transfer
		Type:             "income",
		AttachedDreamID:  "",
		RecurrenceMonths: []int{4, 5, 6}, // April, May, June
	}

	salaryJSON, _ := json.MarshalIndent(salaryRequest, "", "  ")
	fmt.Printf("Request:\n%s\n", salaryJSON)

	fmt.Println("This will create:")
	fmt.Println("- Original: March 15, 2024 - $5,000.00 salary")
	fmt.Println("- Recurring: April 15, 2024 - $5,000.00 salary")
	fmt.Println("- Recurring: May 15, 2024 - $5,000.00 salary")
	fmt.Println("- Recurring: June 15, 2024 - $5,000.00 salary")
	fmt.Println()

	// Example 2: Quarterly Rent
	fmt.Println("Example 2: Quarterly Rent")
	rentRequest := financialsheet.RecurringTransactionRequest{
		Category:         "housing",
		MoneySource:      1,
		Value:            monetary.Amount(150000), // $1,500.00 in cents
		Date:             time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
		PaymentMethod:    3, // Debit card
		Type:             "costs_of_living",
		AttachedDreamID:  "",
		RecurrenceMonths: []int{4, 7, 10}, // April, July, October
	}

	rentJSON, _ := json.MarshalIndent(rentRequest, "", "  ")
	fmt.Printf("Request:\n%s\n", rentJSON)

	fmt.Println("This will create:")
	fmt.Println("- Original: January 1, 2024 - $1,500.00 rent")
	fmt.Println("- Recurring: April 1, 2024 - $1,500.00 rent")
	fmt.Println("- Recurring: July 1, 2024 - $1,500.00 rent")
	fmt.Println("- Recurring: October 1, 2024 - $1,500.00 rent")
	fmt.Println()

	// Example 3: Year-end bonus (next year)
	fmt.Println("Example 3: Year-end Bonus (Next Year)")
	bonusRequest := financialsheet.RecurringTransactionRequest{
		Category:         "compensation",
		MoneySource:      1,
		Value:            monetary.Amount(1000000), // $10,000.00 in cents
		Date:             time.Date(2024, 6, 30, 0, 0, 0, 0, time.UTC),
		PaymentMethod:    2,
		Type:             "income",
		AttachedDreamID:  "",
		RecurrenceMonths: []int{6}, // June (next year)
	}

	bonusJSON, _ := json.MarshalIndent(bonusRequest, "", "  ")
	fmt.Printf("Request:\n%s\n", bonusJSON)

	fmt.Println("This will create:")
	fmt.Println("- Original: June 30, 2024 - $10,000.00 bonus")
	fmt.Println("- Recurring: June 30, 2025 - $10,000.00 bonus (next year)")
	fmt.Println()

	// Example 4: Edge case - February 31st
	fmt.Println("Example 4: Edge Case - Invalid Date Handling")
	edgeCaseRequest := financialsheet.RecurringTransactionRequest{
		Category:         "subscriptions",
		MoneySource:      4,
		Value:            monetary.Amount(2999), // $29.99 in cents
		Date:             time.Date(2024, 1, 31, 0, 0, 0, 0, time.UTC),
		PaymentMethod:    4, // Credit card
		Type:             "expense",
		AttachedDreamID:  "",
		RecurrenceMonths: []int{2}, // February
	}

	edgeCaseJSON, _ := json.MarshalIndent(edgeCaseRequest, "", "  ")
	fmt.Printf("Request:\n%s\n", edgeCaseJSON)

	fmt.Println("This will create:")
	fmt.Println("- Original: January 31, 2024 - $29.99 subscription")
	fmt.Println("- Recurring: March 2, 2024 - $29.99 subscription")
	fmt.Println("  (February 31 automatically adjusted to March 2)")
	fmt.Println()

	fmt.Println("=== Validation Examples ===\n")

	// Invalid examples
	fmt.Println("Invalid Example 1: Empty recurrence months")
	invalidRequest1 := financialsheet.RecurringTransactionRequest{
		RecurrenceMonths: []int{}, // Empty - will fail validation
	}
	fmt.Printf("RecurrenceMonths: %v - ERROR: recurrence months cannot be empty\n", invalidRequest1.RecurrenceMonths)

	fmt.Println("\nInvalid Example 2: Invalid month numbers")
	invalidRequest2 := financialsheet.RecurringTransactionRequest{
		RecurrenceMonths: []int{0, 13}, // Invalid months - will fail validation
	}
	fmt.Printf("RecurrenceMonths: %v - ERROR: invalid month in recurrence array\n", invalidRequest2.RecurrenceMonths)

	fmt.Println("\nInvalid Example 3: Duplicate months")
	invalidRequest3 := financialsheet.RecurringTransactionRequest{
		RecurrenceMonths: []int{4, 5, 4}, // Duplicate - will fail validation
	}
	fmt.Printf("RecurrenceMonths: %v - ERROR: duplicate month in recurrence array\n", invalidRequest3.RecurrenceMonths)

	fmt.Println("\nInvalid Example 4: Same as original month")
	invalidRequest4 := financialsheet.RecurringTransactionRequest{
		Date:             time.Date(2024, 3, 15, 0, 0, 0, 0, time.UTC), // March
		RecurrenceMonths: []int{3, 4, 5}, // Includes March - will fail validation
	}
	fmt.Printf("Original Date: March 2024, RecurrenceMonths: %v\n", invalidRequest4.RecurrenceMonths)
	fmt.Println("ERROR: recurrence month cannot be the same as original transaction month")

	fmt.Println("\n=== Demo Complete ===")
}
