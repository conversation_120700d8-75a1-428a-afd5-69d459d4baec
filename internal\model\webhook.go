package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type DinboraWebhook struct {
	ObjectID    primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID          string             `json:"id,omitempty" bson:"-"`
	Events      []string           `json:"events" bson:"events"`
	Url         string             `json:"url" bson:"url"`
	Description string             `json:"description" bson:"description"`
	Secret      string             `json:"secret" bson:"secret"`
}
