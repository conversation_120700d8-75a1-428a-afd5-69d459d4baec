package stripesubscription

import "github.com/stripe/stripe-go/v72"

type Reader interface {
	Find(id string) (*stripe.Subscription, error)
	FindByCustomer(customer string) ([]*stripe.Subscription, error)
	FindAll() ([]*stripe.Subscription, error)
}

type Writer interface {
	Create(customer string, price string) (*stripe.Subscription, error)
	Cancel(id string) error
	ScheduleCancel(id string) error
}

type Repository interface {
	Reader
	Writer
}
