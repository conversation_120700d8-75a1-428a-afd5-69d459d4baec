package invoice

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
)

type Reader interface {
	Find(ctx context.Context, id string) (*billing.Invoice, error)
	FindAll(ctx context.Context) ([]*billing.Invoice, error)
	FindByContract(ctx context.Context, contract string) ([]*billing.Invoice, error)
	FindByExternalCode(ctx context.Context, externalCode string) (*billing.Invoice, error)
}

type Writer interface {
	Create(ctx context.Context, invoice *billing.Invoice) (string, error)
	Update(ctx context.Context, invoice *billing.Invoice) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
