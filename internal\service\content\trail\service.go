package trail

import (
	"context"
	"fmt"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/cache"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/trail"
)

type Service interface {
	// All Trails CRUD - Regular and Extra
	Find(ctx context.Context, id string) (*content.Trail, error)
	FindAll(ctx context.Context) ([]*content.Trail, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Trail, error)

	FindCardData(ctx context.Context, id string) (*content.TrailCard, error)
	FindAllCardData(ctx context.Context) ([]*content.TrailCard, error)

	// Regular Trails CRUD
	CreateRegularTrail(ctx context.Context, trail *content.Trail) error
	FindRegularTrail(ctx context.Context, id string) (*content.Trail, error)
	FindAllRegularTrails(ctx context.Context) ([]*content.Trail, error)
	FindRegularTrailByIdentifier(ctx context.Context, identifier string) (*content.Trail, error)
	UpdateRegularTrail(ctx context.Context, trail *content.Trail) error
	DeleteRegularTrail(ctx context.Context, id string) error

	FindRegularTrailCardData(ctx context.Context, id string) (*content.TrailCard, error)
	FindAllRegularTrailsCardData(ctx context.Context) ([]*content.TrailCard, error)

	// Lesson CRUD
	CreateRegularLesson(ctx context.Context, trailID string, lesson *content.Lesson) error
	FindRegularLesson(ctx context.Context, trailID string, lessonID string) (*content.Lesson, error)
	UpdateRegularLesson(ctx context.Context, trailID string, lessonID string, lesson *content.Lesson) error
	DeleteRegularLesson(ctx context.Context, trailID string, lessonID string) error

	// Extra Trails CRUD
	CreateExtraTrail(ctx context.Context, trail *content.Trail) error
	FindExtraTrail(ctx context.Context, id string, userClassification string, contractIDs []string) (*content.Trail, error)
	FindAllExtraTrails(ctx context.Context, userClassification string, contractIDs []string) ([]*content.Trail, error)
	FindExtraTrailByIdentifier(ctx context.Context, identifier string, userClassification string, contractIDs []string) (*content.Trail, error)
	UpdateExtraTrail(ctx context.Context, trail *content.Trail) error
	DeleteExtraTrail(ctx context.Context, id string) error

	FindExtraTrailCardData(ctx context.Context, id string, userClassification string, contractIDs []string) (*content.TrailCard, error)
	FindAllExtraTrailsCardData(ctx context.Context, userClassification string, contractIDs []string) ([]*content.TrailCard, error)

	CreateExtraLesson(ctx context.Context, trailID string, userClassification string, contractIDs []string, lesson *content.Lesson) error
	FindExtraLesson(ctx context.Context, trailID string, userClassification string, contractIDs []string, lessonID string) (*content.Lesson, error)
	UpdateExtraLesson(ctx context.Context, trailID string, userClassification string, contractIDs []string, lessonID string, lesson *content.Lesson) error
	DeleteExtraLesson(ctx context.Context, trailID string, userClassification string, contractIDs []string, lessonID string) error

	// Tutorial CRUD
	CreateTutorial(ctx context.Context, tutorial *content.Tutorial) error
	FindTutorial(ctx context.Context, id string) (*content.Tutorial, error)
	FindAllTutorials(ctx context.Context) ([]*content.Tutorial, error)
	FindTutorialByIdentifier(ctx context.Context, identifier string) (*content.Tutorial, error)
	FindTutorialByTicker(ctx context.Context, ticker string) (*content.Tutorial, error)
	UpdateTutorial(ctx context.Context, tutorial *content.Tutorial) error
	DeleteTutorial(ctx context.Context, id string) error

	CreateTutorialLesson(ctx context.Context, tutorialID string, lesson *content.Lesson) error
	FindTutorialLesson(ctx context.Context, tutorialID string, lessonID string) (*content.Lesson, error)
	UpdateTutorialLesson(ctx context.Context, tutorialID string, lessonID string, lesson *content.Lesson) error
	DeleteTutorialLesson(ctx context.Context, tutorialID string, lessonID string) error
}

type service struct {
	Repository trail.Repository
	Cache      cache.CacheService
}

func New(repository trail.Repository, cacheService cache.CacheService) Service {
	return &service{
		Repository: repository,
		Cache:      cacheService,
	}
}

// Find implementations for all trails (regular and extra combined)
func (s *service) Find(ctx context.Context, id string) (*content.Trail, error) {
	cacheKey := trailIDKey(id)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trail, ok := cached.(*content.Trail); ok {
			log.Printf("Cache hit for Find: %s", id)
			return trail, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.Trail failed", cacheKey)
	}

	log.Printf("Cache miss for Find: %s. Fetching from database...", id)
	// Find regular and extra trails
	foundTrail, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}

	foundTrail.ID = foundTrail.ObjectID.Hex()

	errSet := s.Cache.Set(ctx, cacheKey, foundTrail, 0)
	if errSet != nil {
		log.Printf("Error populating cache for trail ID %s: %v", id, errSet)
	} else {
		log.Printf("Cache populated for trail ID: %s", id)
	}

	return foundTrail, nil
}

func (s *service) FindAll(ctx context.Context) ([]*content.Trail, error) {
	cacheKey := trailAllKey

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trails, ok := cached.([]*content.Trail); ok {
			log.Println("Cache hit for FindAll.")
			return trails, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to []*content.Trail failed", cacheKey)
	}

	log.Println("Cache miss for FindAll. Fetching from database...")
	// Get regular and extra trails
	trails, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	errSet := s.Cache.Set(ctx, cacheKey, trails, 0)
	if errSet != nil {
		log.Printf("Error populating FindAll cache: %v", errSet)
	} else {
		log.Printf("FindAll Cache populated with %d trails.", len(trails))
	}

	return trails, nil
}

func (s *service) FindByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	cacheKey := trailIdentifierKey(identifier)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if trail, ok := cached.(*content.Trail); ok {
			log.Printf("Cache hit for FindByIdentifier: %s", identifier)
			return trail, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.Trail failed", cacheKey)
	}

	log.Printf("Cache miss for FindByIdentifier: %s. Fetching from database...", identifier)
	// Find regular and extra trails
	foundTrail, err := s.Repository.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}

	foundTrail.ID = foundTrail.ObjectID.Hex()

	// Cache by both identifier and ID
	idKey := trailIDKey(foundTrail.ID)
	errSetIdentifier := s.Cache.Set(ctx, cacheKey, foundTrail, 0)
	errSetID := s.Cache.Set(ctx, idKey, foundTrail, 0)

	if errSetIdentifier != nil || errSetID != nil {
		log.Printf("Error populating cache for trail identifier %s: identifier_err=%v, id_err=%v", identifier, errSetIdentifier, errSetID)
	} else {
		log.Printf("Cache populated for trail identifier: %s (ID: %s)", identifier, foundTrail.ID)
	}

	return foundTrail, nil
}

func (s *service) FindCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	cacheKey := fmt.Sprintf("trail:card:%s", id)

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if cardData, ok := cached.(*content.TrailCard); ok {
			log.Printf("Cache hit for FindCardData: %s", id)
			return cardData, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion to *content.TrailCard failed", cacheKey)
	}

	log.Printf("Cache miss for FindCardData: %s. Fetching from database...", id)

	// Find regular and extra trails
	cardData, err := s.Repository.FindCardData(ctx, id)
	if err != nil {
		return nil, err
	}

	// Process card data to set ID
	errSet := s.Cache.Set(ctx, cacheKey, cardData, 0)
	if errSet != nil {
		log.Printf("Error populating FindCardData cache: %v", errSet)
	} else {
		log.Printf("FindCardData Cache populated for trail ID: %s", id)
	}

	return cardData, nil
}

func (s *service) FindAllCardData(ctx context.Context) ([]*content.TrailCard, error) {
	cacheKey := "trail:cards:all"

	if cached, found := s.Cache.Get(ctx, cacheKey); found {
		if cardDataList, ok := cached.([]*content.TrailCard); ok {
			log.Println("Cache hit for FindAllCardData.")
			return cardDataList, nil
		}
		log.Printf("Warning: Found item in cache for key %s, but type assertion failed", cacheKey)
	}

	log.Println("Cache miss for FindAllCardData. Fetching from database...")

	// Find regular and extra trails
	cards, err := s.Repository.FindAllCardData(ctx)
	if err != nil {
		return nil, err
	}

	// For extra cards, we can't get them without user context, so we'll return only regular cards
	log.Println("Note: Extra trail cards require user context, returning only regular trail cards")

	errSet := s.Cache.Set(ctx, cacheKey, cards, 0)
	if errSet != nil {
		log.Printf("Error populating FindAllCardData cache: %v", errSet)
	} else {
		log.Printf("FindAllCardData Cache populated with %d trail cards.", len(cards))
	}

	return cards, nil
}

// Cache Key Generation Helpers for All Trails
func trailIDKey(id string) string {
	return fmt.Sprintf("trail:id:%s", id)
}

func trailIdentifierKey(identifier string) string {
	return fmt.Sprintf("trail:identifier:%s", identifier)
}

const trailAllKey = "trail:all"

// Helper function for cache invalidation (all trails)
func (s *service) invalidateAllTrailCache(ctx context.Context, identifier string, id string) {
	errAll := s.Cache.Delete(ctx, trailAllKey)
	if errAll != nil {
		log.Printf("Error invalidating FindAll cache: %v", errAll)
	} else {
		log.Println("Trail FindAll cache invalidated.")
	}

	idKey := trailIDKey(id)
	errID := s.Cache.Delete(ctx, idKey)
	if errID != nil {
		log.Printf("Error invalidating trail cache for ID %s: %v", id, errID)
	}

	if identifier != "" {
		identifierKey := trailIdentifierKey(identifier)
		errIdentifier := s.Cache.Delete(ctx, identifierKey)
		if errIdentifier != nil {
			log.Printf("Error invalidating trail cache for Identifier %s: %v", identifier, errIdentifier)
		}
	}

	if errID == nil && (identifier == "" || s.Cache.Delete(ctx, trailIdentifierKey(identifier)) == nil) {
		log.Printf("Individual trail cache invalidated for ID: %s, Identifier: %s", id, identifier)
	}
}
