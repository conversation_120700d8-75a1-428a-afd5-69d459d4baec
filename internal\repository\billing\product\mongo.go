package product

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	return &mongoDB{
		collection: db.Collection(repository.PRODUCTS_COLLECTION),
	}
}

func (m mongoDB) Create(ctx context.Context, product *billing.Product) error {
	_, err := m.collection.InsertOne(ctx, product)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "product already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create product", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*billing.Product, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid product ID format", errors.Validation, err)
	}

	var product billing.Product
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}}).Decode(&product); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "product not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find product by ID failed", errors.Internal, err)
	}

	return &product, nil
}

func (m mongoDB) FindMany(ctx context.Context, ids []primitive.ObjectID) ([]*billing.Product, error) {
	if len(ids) == 0 {
		return []*billing.Product{}, nil // Return empty slice if no IDs provided
	}

	filter := bson.M{"_id": bson.M{"$in": ids}}
	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "find many products failed", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var products []*billing.Product
	if err = cursor.All(ctx, &products); err != nil {
		return nil, errors.New(errors.Repository, "decode products failed during find many", errors.Internal, err)
	}

	// Note: Unlike FindAll, this doesn't filter out MarketingChannel products.
	// If that's needed, add filtering logic here.

	return products, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*billing.Product, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.New(errors.Repository, "find all products failed", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var products []*billing.Product
	for cursor.Next(ctx) {
		var product billing.Product
		if err = cursor.Decode(&product); err != nil {
			return nil, errors.New(errors.Repository, "decode product failed", errors.Internal, err)
		}

		// Append only if it is not a product linked with a Marketing Channel
		if !product.MarketingChannel {
			products = append(products, &product)
		}
	}

	return products, nil
}

func (m mongoDB) FindByIdentifier(ctx context.Context, identifier string) (*billing.Product, error) {
	var product billing.Product
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&product); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "product by identifier not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find product by identifier failed", errors.Internal, err)
	}

	return &product, nil
}

func (m mongoDB) FindByName(ctx context.Context, name string) (*billing.Product, error) {
	var product billing.Product
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "name", Value: name}}).Decode(&product); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "product by name not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find product by name failed", errors.Internal, err)
	}

	return &product, nil
}

func (m mongoDB) Update(ctx context.Context, product *billing.Product) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: product.ObjectID}},
		primitive.M{"$set": product})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "product update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update product", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "product not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid product ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete product", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "product not found for deletion", errors.NotFound, nil)
	}

	return nil
}
