package contract

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/contract"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Find() echo.HandlerFunc
	FindByCustomer() echo.HandlerFunc
	FindByExternalCode() echo.HandlerFunc
	Cancel() echo.HandlerFunc

	// Cards
	GetCards() echo.HandlerFunc

	// Renewal
	ActivateAutoRenewal() echo.HandlerFunc
	DisableAutoRenewal() echo.HandlerFunc
}

type controller struct {
	Service contract.Service
}

func New(service contract.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (pc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	contractGroup := legacyGroup.Group("billing/contract/", middlewares.AuthGuard())

	// CRUD
	contractGroup.GET(":id/", pc.Find())
	contractGroup.POST("findByCustomer/", pc.FindByCustomer())
	contractGroup.POST("findByExternalCode/", pc.FindByExternalCode())
	contractGroup.DELETE("cancel/:id/", pc.Cancel())

	// Renewal
	contractGroup.PUT("activateAutoRenewal/:id/", pc.ActivateAutoRenewal())
	contractGroup.PUT("disableAutoRenewal/:id/", pc.DisableAutoRenewal())

	// Cards
	contractGroup.POST("getCards/", pc.GetCards())
}

// CRUD
func (pc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		foundContract, err := pc.Service.Find(ctx, sanitizeString(c.Param("id")), userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, foundContract)
	}
}

func (pc *controller) FindByCustomer() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var input map[string]interface{}

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		onlyActive := c.QueryParam("onlyActive") == "true"

		if _, ok := input["customer"]; !ok {
			return errors.New(errors.Controller, "invalid customer id", errors.Validation, nil)
		}

		if val, ok := input["onlyActive"]; ok {
			if val.(string) == "true" {
				onlyActive = true
			}
		}

		foundContract, err := pc.Service.FindByCustomer(ctx, sanitizeString(input["customer"].(string)), onlyActive)
		if err != nil {
			return err
		}

		if foundContract == nil || len(foundContract) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, foundContract)
	}
}

func (pc *controller) FindByExternalCode() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var input map[string]interface{}

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if _, ok := input["code"]; !ok {
			return errors.New(errors.Controller, "code provided is invalid", errors.Validation, nil)
		}

		foundContract, err := pc.Service.FindByExternalCode(ctx, sanitizeString(input["code"].(string)), userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, foundContract)
	}
}

func (pc *controller) Cancel() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		err = pc.Service.Cancel(ctx, sanitizeString(c.Param("id")), userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Cards
func (pc *controller) GetCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var input map[string]interface{}

		if err := c.Bind(&input); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		onlyActive := c.QueryParam("onlyActive") == "true"

		if val, ok := input["onlyActive"]; ok {
			if val.(string) == "true" {
				onlyActive = true
			}
		}

		foundCards, err := pc.Service.GetCards(ctx, userToken.Uid, onlyActive)
		if err != nil {
			return err
		}

		if foundCards == nil || len(foundCards) <= 0 {
			return c.JSON(http.StatusNoContent, nil)
		}

		return c.JSON(http.StatusOK, foundCards)
	}
}

// Renewal
func (pc *controller) ActivateAutoRenewal() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		err = pc.Service.ActivateRenewal(ctx, sanitizeString(c.Param("id")), userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

func (pc *controller) DisableAutoRenewal() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		err = pc.Service.DisableRenewal(ctx, sanitizeString(c.Param("id")), userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Helper
func sanitizeString(str string) string {
	return strings.ToLower(strings.TrimSpace(str))
}
