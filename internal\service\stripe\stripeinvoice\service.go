package stripeinvoice

import (
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripeinvoice"
	"github.com/stripe/stripe-go/v72"
)

type Service interface {
	// Stripe Invoice CRUD
	Find(id string) (*stripe.Invoice, error)
	FindAll() ([]*stripe.Invoice, error)

	// Utility
	Search(params *billing.FilterParams) ([]*stripe.Invoice, error)
}

type service struct {
	Repository stripeinvoice.Repository
}

func New(repository stripeinvoice.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Find(id string) (*stripe.Invoice, error) {
	return s.Repository.Find(id)
}

func (s *service) FindAll() ([]*stripe.Invoice, error) {
	return s.Repository.FindAll()
}

// Utility
func (s *service) Search(params *billing.FilterParams) ([]*stripe.Invoice, error) {
	return s.Repository.Search(params)
}
