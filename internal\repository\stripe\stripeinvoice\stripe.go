package stripeinvoice

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/client"
	"github.com/stripe/stripe-go/v72/invoice"
)

type repository struct {
	client *invoice.Client
}

func New(api *client.API) Repository {
	return &repository{
		client: api.Invoices,
	}
}

func (r *repository) Find(id string) (*stripe.Invoice, error) {
	inv, err := r.client.Get(id, nil)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to get invoice")
	}
	return inv, nil
}

func (r *repository) FindAll() ([]*stripe.Invoice, error) {
	invoices := make([]*stripe.Invoice, 0)
	params := &stripe.InvoiceListParams{}
	params.Filters.AddFilter("limit", "", "100")

	i := r.client.List(params)
	for i.Next() {
		invoices = append(invoices, i.Invoice())
	}

	if err := i.Err(); err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to list invoices")
	}

	return invoices, nil
}

func (r *repository) Search(params *billing.FilterParams) ([]*stripe.Invoice, error) {
	invoices := make([]*stripe.Invoice, 0)
	searchParam := &stripe.InvoiceListParams{}
	searchParam.Filters.AddFilter("limit", "", "100")

	if params.Customer != "" {
		searchParam.Filters.AddFilter("customer", "", params.Customer)
	}
	if params.Status != "" {
		searchParam.Filters.AddFilter("status", "", params.Status)
	}
	if params.Contract != "" {
		searchParam.Filters.AddFilter("subscription", "", params.Contract)
	}

	i := r.client.List(searchParam)
	for i.Next() {
		invoices = append(invoices, i.Invoice())
	}

	if err := i.Err(); err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to search invoices")
	}

	return invoices, nil
}
