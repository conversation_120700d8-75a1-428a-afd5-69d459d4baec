package errors

import (
	"github.com/stripe/stripe-go/v72"
)

func HandleStripeError(layer Layer, err error, message Message) error {
	if stripeErr, ok := err.(*stripe.Error); ok {
		switch stripeErr.Type {
		case stripe.ErrorTypeCard:
			switch stripeErr.Code {
			case stripe.ErrorCodeCardDeclined,
				stripe.ErrorCodeExpiredCard,
				stripe.ErrorCodeIncorrectCVC,
				stripe.ErrorCodeIncorrectZip,
				stripe.ErrorCodeInvalidCVC,
				stripe.ErrorCodeInvalidExpiryMonth,
				stripe.ErrorCodeInvalidExpiryYear,
				stripe.ErrorCodeInvalidNumber:
				return New(layer, message, Validation, err)
			}

		case stripe.ErrorTypeInvalidRequest:
			return New(layer, message, BadRequest, err)

		case stripe.ErrorTypeAPI:
			return New(layer, message, Internal, err)

		case stripe.ErrorTypeAuthentication:
			return New(layer, message, Unauthorized, err)

		case stripe.ErrorTypeRateLimit:
			return New(layer, message, TooManyRequests, err)

		case stripe.ErrorTypeIdempotency:
			return New(layer, message, Conflict, err)
		}

		// Handle specific error codes regardless of type
		switch stripeErr.Code {
		case stripe.ErrorCodeResourceMissing:
			return New(layer, message, NotFound, err)

		case stripe.ErrorCodeResourceAlreadyExists:
			return New(layer, message, Conflict, err)

		case stripe.ErrorCodeParameterInvalidEmpty,
			stripe.ErrorCodeParameterInvalidInteger,
			stripe.ErrorCodeParameterInvalidStringBlank,
			stripe.ErrorCodeParameterInvalidStringEmpty,
			stripe.ErrorCodeParameterMissing,
			stripe.ErrorCodeParameterUnknown:
			return New(layer, message, Validation, err)
		}
	}

	return New(layer, message, Internal, err)
}

func IsStripeErrorCode(err error, code stripe.ErrorCode) bool {
	if stripeError, ok := err.(*stripe.Error); ok {
		if stripeError.Code == code {
			return true
		}
	}

	return false
}
