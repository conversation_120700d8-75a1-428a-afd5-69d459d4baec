package stripecustomer

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripecustomer"
	"github.com/stripe/stripe-go/v72"
)

type Service interface {
	// Stripe Customer CRUD
	Create(user *model.User) (*stripe.Customer, error)
	Find(id string) (*stripe.Customer, error)
	FindAll() ([]*stripe.Customer, error)
	Update(user *model.User) (*stripe.Customer, error)
	Delete(id string) error
}

type service struct {
	repository stripecustomer.Repository
}

func New(repository stripecustomer.Repository) Service {
	return &service{
		repository: repository,
	}
}

// CRUD
func (s *service) Create(user *model.User) (*stripe.Customer, error) {
	if user.ExternalCode != "" {
		return s.Find(user.ExternalCode)
	}

	if user.Email != "" {
		foundCustomer, err := s.repository.FindByEmail(user.Email)
		if err != nil {
			if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) && err.(*errors.DomainError).Kind() != errors.NotFound {
				return nil, err
			}
		}

		if foundCustomer != nil {
			return foundCustomer, nil
		}
	}

	return s.repository.Create(user)
}

func (s *service) Find(id string) (*stripe.Customer, error) {
	return s.repository.Find(id)
}

func (s *service) FindAll() ([]*stripe.Customer, error) {
	return s.repository.FindAll()
}

func (s *service) Update(user *model.User) (*stripe.Customer, error) {
	foundCustomer, err := s.Find(user.ExternalCode)
	if err != nil {
		return nil, err
	}

	return s.repository.Update(foundCustomer.ID, user)
}

func (s *service) Delete(id string) error {
	return s.repository.Delete(id)
}
