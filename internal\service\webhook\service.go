package webhook

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository/webhook"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/stripewebhook"
	"github.com/dsoplabs/dinbora-backend/pkg"
	"github.com/stripe/stripe-go/v72"
)

type Service interface {
	// Webhook CRUD
	Create(ctx context.Context, webhook *model.DinboraWebhook) error
	Find(ctx context.Context, id string) (*model.DinboraWebhook, error)
	FindByUrl(ctx context.Context, url string) (*model.DinboraWebhook, error)
	Update(ctx context.Context, webhook *model.DinboraWebhook) error
	Delete(ctx context.Context, id string) error

	// Sync
	Sync(ctx context.Context, webhook *model.DinboraWebhook) (*model.DinboraWebhook, error)
}

type service struct {
	Repository           webhook.Repository
	StripeWebhookService stripewebhook.Service
}

func New(repository webhook.Repository, stripeWebhookService stripewebhook.Service) Service {
	return &service{
		Repository:           repository,
		StripeWebhookService: stripeWebhookService,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, webhook *model.DinboraWebhook) error {
	foundProduct, err := s.Repository.FindByUrl(ctx, webhook.Url)
	if err == nil && foundProduct != nil {
		return err
	}

	if err = s.Repository.Create(ctx, webhook); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*model.DinboraWebhook, error) {
	foundWebhook, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundWebhook.ID = foundWebhook.ObjectID.Hex()
	return foundWebhook, nil
}

func (s *service) FindByUrl(ctx context.Context, url string) (*model.DinboraWebhook, error) {
	foundWebhook, err := s.Repository.FindByUrl(ctx, url)
	if err != nil {
		return nil, err
	}
	foundWebhook.ID = foundWebhook.ObjectID.Hex()
	return foundWebhook, nil
}

func (s *service) Update(ctx context.Context, webhook *model.DinboraWebhook) error {
	return s.Repository.Update(ctx, webhook)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}

// Sync
func (s *service) Sync(ctx context.Context, webhook *model.DinboraWebhook) (*model.DinboraWebhook, error) {
	foundDinboraWebhook, err := s.FindByUrl(ctx, webhook.Url)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
	}

	if foundDinboraWebhook != nil {
		return foundDinboraWebhook, nil
	} else {
		foundStripeWebhook, err := s.StripeWebhookService.FindByUrl(webhook.Url)
		if err != nil {
			if err.(*errors.DomainError).Kind() != errors.NotFound {
				return nil, err
			}
		}

		if foundStripeWebhook != nil {
			err = s.StripeWebhookService.Delete(foundStripeWebhook.ID)
			if err != nil {
				return nil, err
			}
		}

		createdStripeWebhook, err := s.StripeWebhookService.Create(dinboraWebhookToStripeWebhook(webhook))
		if err != nil {
			return nil, err
		}

		webhook.Secret, err = pkg.Encrypt(createdStripeWebhook.Secret)
		if err != nil {
			return nil, err
		}

		err = s.Create(ctx, webhook)
		if err != nil {
			return nil, err
		}
	}

	return s.FindByUrl(ctx, webhook.Url)
}

// Helper
func dinboraWebhookToStripeWebhook(dinboraWebhook *model.DinboraWebhook) *stripe.WebhookEndpoint {
	return &stripe.WebhookEndpoint{
		Description:   dinboraWebhook.Description,
		EnabledEvents: dinboraWebhook.Events,
		URL:           dinboraWebhook.Url,
	}
}
