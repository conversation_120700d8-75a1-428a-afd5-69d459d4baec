package webhook

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	return &mongoDB{
		collection: db.Collection(repository.WEBHOOKS_COLLECTION),
	}
}

func (m mongoDB) Create(ctx context.Context, webhook *model.DinboraWebhook) error {
	_, err := m.collection.InsertOne(ctx, webhook)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "webhook already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create webhook", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*model.DinboraWebhook, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid webhook ID format", errors.Validation, err)
	}

	var dinboraWebhook model.DinboraWebhook
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&dinboraWebhook); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "webhook not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find webhook", errors.Internal, err)
	}

	dinboraWebhook.ID = dinboraWebhook.ObjectID.Hex()
	return &dinboraWebhook, nil
}

func (m mongoDB) FindByUrl(ctx context.Context, url string) (*model.DinboraWebhook, error) {
	var dinboraWebhook model.DinboraWebhook
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "url", Value: url}}).Decode(&dinboraWebhook); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "webhook with URL not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find webhook by URL", errors.Internal, err)
	}

	dinboraWebhook.ID = dinboraWebhook.ObjectID.Hex()
	return &dinboraWebhook, nil
}

func (m mongoDB) Update(ctx context.Context, webhook *model.DinboraWebhook) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: webhook.ObjectID}},
		primitive.M{"$set": webhook})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "webhook update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update webhook", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "webhook not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid webhook ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete webhook", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "webhook not found for deletion", errors.NotFound, nil)
	}

	return nil
}
