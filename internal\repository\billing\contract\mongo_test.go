package contract

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/config"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoRepositoryTestSuite struct {
	suite.Suite
	client     *mongo.Client
	db         *mongo.Database
	repository Repository
	testIDs    []string
}

func (suite *MongoRepositoryTestSuite) SetupTest() {
	ctx := context.Background()

	// Initialize environment configuration
	err := config.EnvFromFile("../../../../.env")
	if err != nil {
		suite.T().Skipf("Failed to initialize environment configuration: %v - skipping integration tests", err)
		return
	}

	// Get database configuration
	dbURL := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DATABASE_NAME")

	if dbURL == "" || dbName == "" {
		suite.T().Skip("DATABASE_URL and DATABASE_NAME configuration not set - skipping integration tests")
		return
	}

	// Connect to MongoDB
	clientOptions := options.Client().ApplyURI(dbURL)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		suite.T().Skipf("Failed to connect to MongoDB: %v - skipping integration tests", err)
		return
	}
	suite.client = client

	// Verify MongoDB connection
	err = suite.client.Ping(ctx, nil)
	if err != nil {
		suite.T().Skipf("Failed to ping MongoDB: %v - skipping integration tests", err)
		return
	}

	suite.db = suite.client.Database(dbName)
	suite.repository = New(suite.db)

	// Verify test collection exists
	collections, err := suite.db.ListCollectionNames(ctx, bson.M{})
	if err != nil {
		suite.T().Skipf("Failed to list collections: %v - skipping integration tests", err)
		return
	}

	var collectionExists bool
	for _, col := range collections {
		if col == repository.CONTRACTS_COLLECTION {
			collectionExists = true
			break
		}
	}

	if !collectionExists {
		suite.T().Skipf("Collection %s does not exist - skipping integration tests", repository.CONTRACTS_COLLECTION)
		return
	}
}

func (suite *MongoRepositoryTestSuite) TearDownTest() {
	ctx := context.Background()

	// Clean up test data
	if suite.db != nil && len(suite.testIDs) > 0 {
		for _, id := range suite.testIDs {
			objID, err := primitive.ObjectIDFromHex(id)
			if err == nil {
				suite.T().Logf("Deleting test contract with ID: %s", id)
				res, err := suite.db.Collection(repository.CONTRACTS_COLLECTION).DeleteOne(ctx, bson.M{"_id": objID})
				suite.NoError(err)
				suite.T().Logf("Delete result: %d document(s) deleted", res.DeletedCount)
			}
		}
	}

	// Disconnect client
	if suite.client != nil {
		err := suite.client.Disconnect(ctx)
		if err != nil {
			suite.T().Errorf("Error disconnecting client: %v", err)
		}
	}
}

func TestMongoRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(MongoRepositoryTestSuite))
}

func (suite *MongoRepositoryTestSuite) TestIndexCreation() {
	ctx := context.Background()

	// Get indexes
	cursor, err := suite.db.Collection(repository.CONTRACTS_COLLECTION).Indexes().List(ctx)
	suite.Require().NoError(err)
	defer cursor.Close(ctx)

	var indexes []bson.M
	err = cursor.All(ctx, &indexes)
	suite.Require().NoError(err)

	// Find our specific index
	var found bool
	for _, index := range indexes {
		if index["name"] == "customer_1" { // MongoDB default naming for single field index
			found = true
			// Verify index properties
			key := index["key"].(bson.M)
			suite.Equal(int32(1), key["customer"]) // MongoDB returns int32 for index value
			break
		}
	}
	suite.True(found, "Customer index not found")
}

func (suite *MongoRepositoryTestSuite) TestCRUDOperations() {
	ctx := context.Background()

	// Create
	now := time.Now()
	paymentData := &billing.PaymentData{
		PaymentDate:      now,
		AutomaticRenewal: true,
		ExpirationDate:   now.Add(30 * 24 * time.Hour),
	}
	contract := &billing.Contract{
		ObjectID:     primitive.NewObjectID(),
		Customer:     "test_customer",
		Plan:         "test_plan",
		Status:       billing.ACTIVE,
		PaymentData:  paymentData,
		ExternalCode: "test_external_code",
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err := suite.repository.Create(ctx, contract)
	suite.Require().NoError(err)
	suite.testIDs = append(suite.testIDs, contract.ObjectID.Hex())

	// Find by ID
	found, err := suite.repository.Find(ctx, contract.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal(contract.Customer, found.Customer)
	suite.Equal(contract.Status, found.Status)
	suite.Equal(contract.Plan, found.Plan)

	// Find by Customer
	contracts, err := suite.repository.FindByCustomer(ctx, contract.Customer, false)
	suite.Require().NoError(err)
	suite.Len(contracts, 1)
	suite.Equal(contract.Customer, contracts[0].Customer)

	// Find by External Code
	found, err = suite.repository.FindByExternalCode(ctx, contract.ExternalCode)
	suite.Require().NoError(err)
	suite.Equal(contract.ExternalCode, found.ExternalCode)

	// Find by Customer with onlyActive true
	contracts, err = suite.repository.FindByCustomer(ctx, contract.Customer, true)
	suite.Require().NoError(err)
	suite.Len(contracts, 1) // Should find because contract is ACTIVE

	// Update contract status to CANCELLED
	contract.Status = billing.CANCELLED
	err = suite.repository.Update(ctx, contract)
	suite.Require().NoError(err)

	// Find by Customer with onlyActive true after cancellation
	contracts, err = suite.repository.FindByCustomer(ctx, contract.Customer, true)
	suite.Require().NoError(err)
	suite.Len(contracts, 0) // Should not find because contract is now CANCELLED

	// Delete
	err = suite.repository.Delete(ctx, contract.ObjectID.Hex())
	suite.Require().NoError(err)

	// Verify deletion
	_, err = suite.repository.Find(ctx, contract.ObjectID.Hex())
	suite.Error(err) // Should return not found error
}

func (suite *MongoRepositoryTestSuite) TestErrorCases() {
	ctx := context.Background()

	// Test invalid ID format
	_, err := suite.repository.Find(ctx, "invalid-id")
	suite.Error(err)

	// Test not found
	_, err = suite.repository.Find(ctx, primitive.NewObjectID().Hex())
	suite.Error(err)

	// Test update not found
	contract := &billing.Contract{
		ObjectID: primitive.NewObjectID(),
		Customer: "test_customer",
		Plan:     "test_plan",
		Status:   billing.ACTIVE,
	}
	err = suite.repository.Update(ctx, contract)
	suite.Error(err)

	// Test delete not found
	err = suite.repository.Delete(ctx, primitive.NewObjectID().Hex())
	suite.Error(err)
}

func (suite *MongoRepositoryTestSuite) TestContractStatuses() {
	ctx := context.Background()
	now := time.Now()

	// Create contracts with different statuses
	statuses := []billing.Status{billing.ACTIVE, billing.CANCELLED, billing.DISABLED, billing.NOT_COMPLETED}
	for _, status := range statuses {
		contract := &billing.Contract{
			ObjectID:  primitive.NewObjectID(),
			Customer:  "test_customer",
			Plan:      "test_plan",
			Status:    status,
			CreatedAt: now,
			UpdatedAt: now,
		}

		err := suite.repository.Create(ctx, contract)
		suite.Require().NoError(err)
		suite.testIDs = append(suite.testIDs, contract.ObjectID.Hex())
	}

	// Find all contracts
	contracts, err := suite.repository.FindByCustomer(ctx, "test_customer", false)
	suite.Require().NoError(err)
	suite.Len(contracts, len(statuses))

	// Find only active contracts
	activeContracts, err := suite.repository.FindByCustomer(ctx, "test_customer", true)
	suite.Require().NoError(err)
	suite.Len(activeContracts, 1)
	suite.Equal(billing.ACTIVE, activeContracts[0].Status)
}
