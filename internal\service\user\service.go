package user

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository/user"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/rbac"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/stripecustomer"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
)

type Service interface {
	// CRUD
	Create(ctx context.Context, user *model.User, referralCode string) error
	Find(ctx context.Context, id string) (*model.User, error)
	FindAll(ctx context.Context) ([]*model.User, error)
	FindByEmail(ctx context.Context, email string) (*model.User, error)
	FindByReferral(ctx context.Context, referralCode string) (*model.User, error)
	FindDeletedByEmail(ctx context.Context, email string) (*model.User, error)

	// External Integration - Stripe
	FindByExternalCode(ctx context.Context, externalCode string) (*model.User, error)
	FindDeletedByExternalCode(context.Context, string) (*model.User, error)

	// External Integration - Kiwify
	FindByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error)
	FindDeletedByExternalCodeKiwify(context.Context, string) (*model.User, error)

	Update(ctx context.Context, user *model.User) error
	Patch(ctx context.Context, user *model.User, patchData *model.User) error
	Sync(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id string, reason *model.DeleteReason) error

	// Card CRUD
	FindCard(ctx context.Context, userId string) (*model.UserCard, error)

	// Role-based filtering methods
	FindAllWithRoleFilter(ctx context.Context, requestingUserID string, requestingUserRoles []string) ([]*model.User, error)
	FindWithRoleFilter(ctx context.Context, targetUserID string, requestingUserID string, requestingUserRoles []string) (*model.User, error)
	CanAccessUser(ctx context.Context, requestingUserID string, requestingUserRoles []string, targetUserID string) (bool, error)

	// HR-specific methods
	FindEmployeesByHR(ctx context.Context, hrUserID string, hrUserRoles []string) ([]*model.User, error)

	// Utility
	OnlyAdmin(ctx context.Context, id string) error
}

type service struct {
	Repository            user.Repository
	DreamboardService     dreamboard.Service
	FinancialDNAService   financialdna.Service
	FinancialSheetService financialsheet.Service
	ProgressionService    progression.Service
	VaultService          vault.Service
	ContractService       contract.Service
	WalletService         wallet.Service
	CustomerService       stripecustomer.Service
	RBACService           rbac.Service
}

func New(repository user.Repository, dreamboardService dreamboard.Service, financialdnaService financialdna.Service, financialsheetService financialsheet.Service, progressionService progression.Service, vaultService vault.Service, contractService contract.Service, walletService wallet.Service, customerService stripecustomer.Service, rbacService rbac.Service) Service {
	return &service{
		Repository:            repository,
		DreamboardService:     dreamboardService,
		FinancialDNAService:   financialdnaService,
		FinancialSheetService: financialsheetService,
		ProgressionService:    progressionService,
		VaultService:          vaultService,
		ContractService:       contractService,
		WalletService:         walletService,
		CustomerService:       customerService,
		RBACService:           rbacService,
	}
}
