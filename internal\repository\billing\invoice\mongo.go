package invoice

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	return &mongoDB{
		collection: db.Collection(repository.INVOICES_COLLECTION),
	}
}

func (m *mongoDB) Create(ctx context.Context, invoice *billing.Invoice) (string, error) {
	insertedResult, err := m.collection.InsertOne(ctx, invoice)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, "invoice already exists", errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, "failed to create invoice", errors.Internal, err)
	}

	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m *mongoDB) Find(ctx context.Context, id string) (*billing.Invoice, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid invoice ID format", errors.Validation, err)
	}

	var invoice billing.Invoice
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}}).Decode(&invoice); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "invoice not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find invoice by ID failed", errors.Internal, err)
	}

	invoice.ID = invoice.ObjectID.Hex()
	return &invoice, nil
}

func (m *mongoDB) FindAll(ctx context.Context) ([]*billing.Invoice, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.New(errors.Repository, "find all invoices failed", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var invoices []*billing.Invoice
	for cursor.Next(ctx) {
		var invoice billing.Invoice
		if err = cursor.Decode(&invoice); err != nil {
			return nil, errors.New(errors.Repository, "decode invoice failed", errors.Internal, err)
		}
		invoice.ID = invoice.ObjectID.Hex()
		invoices = append(invoices, &invoice)
	}

	return invoices, nil
}

func (m *mongoDB) FindByContract(ctx context.Context, contract string) ([]*billing.Invoice, error) {
	cursor, err := m.collection.Find(ctx, bson.D{primitive.E{Key: "contract", Value: contract}})
	if err != nil {
		return nil, errors.New(errors.Repository, "find invoices by contract failed", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var invoices []*billing.Invoice
	for cursor.Next(ctx) {
		var invoice billing.Invoice
		if err = cursor.Decode(&invoice); err != nil {
			return nil, errors.New(errors.Repository, "decode invoice failed", errors.Internal, err)
		}
		invoice.ID = invoice.ObjectID.Hex()
		invoices = append(invoices, &invoice)
	}

	return invoices, nil
}

func (m *mongoDB) FindByExternalCode(ctx context.Context, externalCode string) (*billing.Invoice, error) {
	var invoice billing.Invoice
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "externalCode", Value: externalCode}}).Decode(&invoice); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "invoice by external code not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "find invoice by external code failed", errors.Internal, err)
	}

	invoice.ID = invoice.ObjectID.Hex()
	return &invoice, nil
}

func (m *mongoDB) Update(ctx context.Context, invoice *billing.Invoice) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: invoice.ObjectID}},
		primitive.M{"$set": invoice})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "invoice update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update invoice", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "invoice not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m *mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid invoice ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete invoice", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "invoice not found for deletion", errors.NotFound, nil)
	}

	return nil
}
