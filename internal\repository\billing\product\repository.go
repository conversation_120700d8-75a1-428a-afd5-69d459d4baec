package product

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	Find(ctx context.Context, id string) (*billing.Product, error)
	FindMany(ctx context.Context, ids []primitive.ObjectID) ([]*billing.Product, error)
	FindAll(ctx context.Context) ([]*billing.Product, error)
	FindByIdentifier(ctx context.Context, identifier string) (*billing.Product, error)
	FindByName(ctx context.Context, name string) (*billing.Product, error)
}

type Writer interface {
	Create(ctx context.Context, product *billing.Product) error
	Update(ctx context.Context, product *billing.Product) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
