package stripesubscription

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/client"
	"github.com/stripe/stripe-go/v72/sub"
)

type repository struct {
	client *sub.Client
}

func New(api *client.API) Repository {
	return &repository{
		client: api.Subscriptions,
	}
}

func (r *repository) Create(customer string, price string) (*stripe.Subscription, error) {
	subscriptionParams := &stripe.SubscriptionParams{
		Customer: stripe.String(customer),
		Items: []*stripe.SubscriptionItemsParams{
			{
				Price: stripe.String(price),
			},
		},
		PaymentBehavior: stripe.String("default_incomplete"),
		PaymentSettings: &stripe.SubscriptionPaymentSettingsParams{
			PaymentMethodTypes: stripe.StringSlice([]string{
				"card",
			}),
		},
	}
	subscriptionParams.AddExpand("latest_invoice.payment_intent")

	subscription, err := r.client.New(subscriptionParams)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to create subscription")
	}

	return subscription, nil
}

func (r *repository) Find(id string) (*stripe.Subscription, error) {
	subscription, err := r.client.Get(id, nil)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to get subscription")
	}
	return subscription, nil
}

func (r *repository) FindAll() ([]*stripe.Subscription, error) {
	subscriptions := make([]*stripe.Subscription, 0)
	params := &stripe.SubscriptionListParams{}

	i := r.client.List(params)
	if err := i.Err(); err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to list subscriptions")
	}

	for i.Next() {
		subscriptions = append(subscriptions, i.Subscription())
	}

	return subscriptions, nil
}

func (r *repository) FindByCustomer(customer string) ([]*stripe.Subscription, error) {
	subscriptions := make([]*stripe.Subscription, 0)
	params := &stripe.SubscriptionListParams{
		Customer: customer,
	}

	i := r.client.List(params)
	for i.Next() {
		if err := i.Err(); err != nil {
			return nil, errors.HandleStripeError(errors.Repository, err, "failed to list subscriptions by customer")
		}
		subscriptions = append(subscriptions, i.Subscription())
	}

	return subscriptions, nil
}

func (r *repository) ScheduleCancel(id string) error {
	_, err := r.client.Update(id, &stripe.SubscriptionParams{
		CancelAtPeriodEnd: stripe.Bool(true),
	})
	if err != nil {
		return errors.HandleStripeError(errors.Repository, err, "failed to schedule subscription cancellation")
	}
	return nil
}

func (r *repository) Cancel(id string) error {
	_, err := r.client.Cancel(id, &stripe.SubscriptionCancelParams{})
	if err != nil {
		return errors.HandleStripeError(errors.Repository, err, "failed to cancel subscription")
	}
	return nil
}
