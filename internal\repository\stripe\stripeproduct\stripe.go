package stripeproduct

import (
	"math"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripeprice"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/client"
	"github.com/stripe/stripe-go/v72/product"
)

type repository struct {
	client *product.Client
	price  stripeprice.Repository
}

func New(api *client.API, price stripeprice.Repository) Repository {
	return &repository{
		client: api.Products,
		price:  price,
	}
}

func (r *repository) Create(product *billing.Product) (*stripe.Product, error) {
	params := &stripe.ProductParams{
		Active:      stripe.Bool(true),
		Name:        stripe.String(product.Name),
		Description: stripe.String(product.Description),
	}

	if product.Pricing.ExternalCode != "" {
		foundPrice, err := r.price.Find(product.Pricing.ExternalCode)
		if err != nil {
			if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) {
				return nil, err
			}
		}

		if foundPrice != nil {
			updatedPrice, err := r.price.Update(product.Pricing.ExternalCode, product)
			if err != nil {
				return nil, err
			}
			params.DefaultPrice = stripe.String(updatedPrice.ID)
			clientUpdate, err := r.client.Update(product.ID, params)
			if err != nil {
				return nil, errors.HandleStripeError(errors.Repository, err, "failed to create product")
			}
			return clientUpdate, nil
		}

		lookupKeyPrice, err := r.price.FindByLookUpKey(product.Identifier)
		if err != nil {
			if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) {
				return nil, err
			}
		}

		if lookupKeyPrice != nil {
			params.DefaultPrice = stripe.String(lookupKeyPrice.ID)

			clientUpdate, err := r.client.Update(product.ID, params)
			if err != nil {
				return nil, errors.HandleStripeError(errors.Repository, err, "failed to create product")
			}
			return clientUpdate, nil
		}

		createdPrice, err := r.price.Create(product, true)
		if err != nil {
			return nil, err
		}
		params.DefaultPrice = stripe.String(createdPrice.ID)
		clientUpdate, err := r.client.Update(product.ID, params)
		if err != nil {
			return nil, errors.HandleStripeError(errors.Repository, err, "failed to create product")
		}
		return clientUpdate, nil
	}

	foundPrice, err := r.price.FindByLookUpKey(product.Identifier)
	if err != nil {
		if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) {
			return nil, err
		}
	}

	if foundPrice != nil {
		params.DefaultPrice = stripe.String(foundPrice.ID)
		result, err := r.client.Update(product.ID, params)
		if err != nil {
			return nil, errors.HandleStripeError(errors.Repository, err, "failed to update product")
		}
		return result, nil
	}

	createdPrice, err := r.price.Create(product, true)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to create price")
	}
	params.DefaultPrice = stripe.String(createdPrice.ID)
	result, err := r.client.Update(product.ID, params)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to update product")
	}
	return result, nil
}

func (r *repository) Find(id string) (*stripe.Product, error) {
	product, err := r.client.Get(id, nil)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to find product")
	}
	return product, nil
}

func (r *repository) FindAll() ([]*stripe.Product, error) {
	products := make([]*stripe.Product, 0)
	params := &stripe.ProductListParams{}
	params.Filters.AddFilter("limit", "", "3")

	i := r.client.List(params)
	for i.Next() {
		products = append(products, i.Product())
	}
	if err := i.Err(); err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to list products")
	}
	return products, nil
}

func (r *repository) Update(id string, product *billing.Product) (*stripe.Product, error) {
	params := &stripe.ProductParams{
		Active:      stripe.Bool(true),
		Name:        stripe.String(product.Name),
		Description: stripe.String(product.Description),
	}

	if product.Pricing.ExternalCode != "" {
		currentPrice, err := r.price.Find(product.Pricing.ExternalCode)
		if err != nil {
			if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) {
				return nil, err
			}

			foundPrice, err := r.price.FindByLookUpKey(product.Identifier)
			if err != nil {
				if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) {
					return nil, err
				}
			}

			if foundPrice != nil {
				params.DefaultPrice = stripe.String(foundPrice.ID)
			} else {
				createdPrice, err := r.price.Create(product, false)
				if err != nil {
					return nil, err
				}
				params.DefaultPrice = stripe.String(createdPrice.ID)
			}

			updatedProduct, err := r.client.Update(id, params)
			if err != nil {
				return nil, errors.HandleStripeError(errors.Repository, err, "failed to update product")
			}
			return updatedProduct, nil
		}

		if currentPrice.UnitAmount != int64(math.Floor(product.Pricing.Value*100)) {
			if err := r.price.RemoveLookupKey(currentPrice.ID); err != nil {
				return nil, err
			}
			createdPrice, err := r.price.Create(product, false)
			if err != nil {
				return nil, err
			}
			params.DefaultPrice = stripe.String(createdPrice.ID)
		} else {
			params.DefaultPrice = stripe.String(currentPrice.ID)
		}
	} else {
		foundPrice, err := r.price.FindByLookUpKey(product.Identifier)
		if err != nil {
			if !errors.IsStripeErrorCode(err, stripe.ErrorCodeResourceMissing) {
				return nil, err
			}
		}

		if foundPrice != nil {
			params.DefaultPrice = stripe.String(foundPrice.ID)
		} else {
			createdPrice, err := r.price.Create(product, false)
			if err != nil {
				return nil, err
			}
			params.DefaultPrice = stripe.String(createdPrice.ID)
		}
	}

	updatedProduct, err := r.client.Update(id, params)
	if err != nil {
		return nil, errors.HandleStripeError(errors.Repository, err, "failed to update product")
	}
	return updatedProduct, nil
}

func (r *repository) Delete(id string) error {
	_, err := r.client.Del(id, nil)
	if err != nil {
		return errors.HandleStripeError(errors.Repository, err, "failed to delete product")
	}
	return nil
}
