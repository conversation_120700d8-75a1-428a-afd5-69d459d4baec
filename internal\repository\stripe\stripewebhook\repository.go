package stripewebhook

import (
	"github.com/stripe/stripe-go/v72"
)

type Reader interface {
	Find(id string) (*stripe.WebhookEndpoint, error)
	FindAll() ([]*stripe.WebhookEndpoint, error)
	FindByUrl(url string) (*stripe.WebhookEndpoint, error)
}

type Writer interface {
	Create(webhook *stripe.WebhookEndpoint) (*stripe.WebhookEndpoint, error)
	Update(id string, webhook *stripe.WebhookEndpoint) (*stripe.WebhookEndpoint, error)
	Delete(id string) error
}

type Repository interface {
	Reader
	Writer
}
