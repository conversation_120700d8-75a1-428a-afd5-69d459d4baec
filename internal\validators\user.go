package validators

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
)

type UserValidator interface {
	// Core
	ContextUser(contextUser *model.User)
	ValidateUserRoles(ctx context.Context, oldUser, newUser *model.User) error
}

type validator struct {
	requestUser *model.User
	service     user.Service
}

func NewUserValidator(contextUser *model.User, service user.Service) UserValidator {
	return &validator{
		requestUser: contextUser,
		service:     service,
	}
}

// Core
func (uv *validator) ContextUser(contextUser *model.User) {
	uv.requestUser = contextUser
}

func (uv *validator) ValidateUserRoles(ctx context.Context, oldUser *model.User, newUser *model.User) error {
	if oldUser.IsAdmin() && !newUser.IsAdmin() {
		err := uv.service.OnlyAdmin(ctx, oldUser.ID)
		if err != nil {
			return err
		}
	}

	if !oldUser.IsAdmin() && newUser.IsAdmin() && !uv.requestUser.IsAdmin() {
		return errors.New(errors.Controller, "user forbidden", errors.Forbidden, nil)
	}

	return nil
}
