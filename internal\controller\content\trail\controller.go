package trail

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// All Trails CRUD - Regular and Extra with user access.
	Find() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	FindByIdentifier() echo.HandlerFunc

	FindCardData() echo.HandlerFunc
	FindAllCardData() echo.HandlerFunc

	// Regular Trail CRUD
	CreateRegularTrail() echo.HandlerFunc
	FindRegularTrail() echo.HandlerFunc
	FindAllRegularTrails() echo.HandlerFunc
	FindRegularTrailByIdentifier() echo.HandlerFunc
	UpdateRegularTrail() echo.HandlerFunc
	PatchRegularTrail() echo.HandlerFunc
	DeleteRegularTrail() echo.HandlerFunc

	FindRegularTrailCardData() echo.HandlerFunc
	FindAllRegularTrailsCardData() echo.HandlerFunc

	CreateRegularLesson() echo.HandlerFunc
	FindRegularLesson() echo.HandlerFunc
	UpdateRegularLesson() echo.HandlerFunc
	PatchRegularLesson() echo.HandlerFunc
	DeleteRegularLesson() echo.HandlerFunc

	// Extra Trails CRUD
	CreateExtraTrail() echo.HandlerFunc
	FindExtraTrail() echo.HandlerFunc
	FindAllExtraTrails() echo.HandlerFunc
	FindExtraTrailByIdentifier() echo.HandlerFunc
	UpdateExtraTrail() echo.HandlerFunc
	PatchExtraTrail() echo.HandlerFunc
	DeleteExtraTrail() echo.HandlerFunc

	FindExtraTrailCardData() echo.HandlerFunc
	FindAllExtraTrailsCardData() echo.HandlerFunc

	CreateExtraLesson() echo.HandlerFunc
	FindExtraLesson() echo.HandlerFunc
	UpdateExtraLesson() echo.HandlerFunc
	PatchExtraLesson() echo.HandlerFunc
	DeleteExtraLesson() echo.HandlerFunc

	// Tutorial CRUD
	CreateTutorial() echo.HandlerFunc
	FindTutorial() echo.HandlerFunc
	FindAllTutorials() echo.HandlerFunc
	FindTutorialByIdentifier() echo.HandlerFunc
	FindTutorialByTicker() echo.HandlerFunc
	UpdateTutorial() echo.HandlerFunc
	PatchTutorial() echo.HandlerFunc
	DeleteTutorial() echo.HandlerFunc

	CreateTutorialLesson() echo.HandlerFunc
	FindTutorialLesson() echo.HandlerFunc
	UpdateTutorialLesson() echo.HandlerFunc
	DeleteTutorialLesson() echo.HandlerFunc
}

type controller struct {
	Service         trail.Service
	UserService     user.Service
	ContractService contract.Service
}

func New(service trail.Service, userService user.Service, contractService contract.Service) Controller {
	return &controller{
		Service:         service,
		UserService:     userService,
		ContractService: contractService,
	}
}

// Routes
func (tc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	// V2 API
	trailsGroup := currentGroup.Group("/contents/trails", middlewares.AuthGuard())

	// CRUD operations
	trailsGroup.GET("/me/:id", tc.Find())
	trailsGroup.GET("/me", tc.FindAll())
	trailsGroup.GET("/me/identifier/:identifier", tc.FindByIdentifier())

	trailsGroup.GET("/me/cards/:id", tc.FindCardData())
	trailsGroup.GET("/me/cards", tc.FindAllCardData())

	// Regular Trail CRUD
	trailsGroup.POST("/regular", tc.CreateRegularTrail(), middlewares.AdminGuard())
	trailsGroup.GET("/me/regular/:id", tc.FindRegularTrail())
	trailsGroup.GET("/me/regular", tc.FindAllRegularTrails())
	trailsGroup.GET("/me/regular/identifier/:identifier", tc.FindRegularTrailByIdentifier())
	trailsGroup.PUT("/regular/:id", tc.UpdateRegularTrail(), middlewares.AdminGuard())
	trailsGroup.PATCH("/regular/:id", tc.PatchRegularTrail(), middlewares.AdminGuard())
	trailsGroup.DELETE("/regular/:id", tc.DeleteRegularTrail(), middlewares.AdminGuard())

	trailsGroup.GET("/me/regular/cards/:id", tc.FindRegularTrailCardData())
	trailsGroup.GET("/me/regular/cards", tc.FindAllRegularTrailsCardData())

	trailsGroup.POST("/regular/:id/lessons", tc.CreateRegularLesson(), middlewares.AdminGuard())
	trailsGroup.GET("/me/regular/:id/lessons/:identifier", tc.FindRegularLesson())
	trailsGroup.PUT("/regular/:id/lessons/:identifier", tc.UpdateRegularLesson(), middlewares.AdminGuard())
	trailsGroup.PATCH("/regular/:id/lessons/:identifier", tc.PatchRegularLesson(), middlewares.AdminGuard())
	trailsGroup.DELETE("/regular/:id/lessons/:identifier", tc.DeleteRegularLesson(), middlewares.AdminGuard())

	// Extra Trail CRUD
	trailsGroup.POST("/extra", tc.CreateExtraTrail(), middlewares.AdminGuard())
	trailsGroup.GET("/me/extra/:id", tc.FindExtraTrail())
	trailsGroup.GET("/me/extra", tc.FindAllExtraTrails())
	trailsGroup.GET("/me/extra/identifier/:identifier", tc.FindExtraTrailByIdentifier())
	trailsGroup.PUT("/extra/:id", tc.UpdateExtraTrail(), middlewares.AdminGuard())
	trailsGroup.DELETE("/extra/:id", tc.DeleteExtraTrail(), middlewares.AdminGuard())

	trailsGroup.GET("/me/extra/cards/:id", tc.FindExtraTrailCardData())
	trailsGroup.GET("/me/extra/cards", tc.FindAllExtraTrailsCardData())

	trailsGroup.POST("/extra/:id/lessons", tc.CreateExtraLesson(), middlewares.AdminGuard())
	trailsGroup.GET("/me/extra/:id/lessons/:identifier", tc.FindExtraLesson())
	trailsGroup.PUT("/extra/:id/lessons/:identifier", tc.UpdateExtraLesson(), middlewares.AdminGuard())
	trailsGroup.PATCH("/extra/:id/lessons/:identifier", tc.PatchExtraLesson(), middlewares.AdminGuard())
	trailsGroup.DELETE("/extra/:id/lessons/:identifier", tc.DeleteExtraLesson(), middlewares.AdminGuard())

	// Tutorial CRUD
	trailsGroup.POST("/tutorials", tc.CreateTutorial(), middlewares.AdminGuard())
	trailsGroup.GET("/me/tutorials/:id", tc.FindTutorial())
	trailsGroup.GET("/me/tutorials", tc.FindAllTutorials())
	trailsGroup.GET("/me/tutorials/identifier/:identifier", tc.FindTutorialByIdentifier())
	trailsGroup.GET("/me/tutorials/ticker/:ticker", tc.FindTutorialByTicker())
	trailsGroup.PUT("/tutorials/:id", tc.UpdateTutorial(), middlewares.AdminGuard())
	trailsGroup.PATCH("/tutorials/:id", tc.PatchTutorial(), middlewares.AdminGuard())
	trailsGroup.DELETE("/tutorials/:id", tc.DeleteTutorial(), middlewares.AdminGuard())

	trailsGroup.POST("/tutorials/:id/lessons", tc.CreateTutorialLesson(), middlewares.AdminGuard())
	trailsGroup.GET("/me/tutorials/:id/lessons/:identifier", tc.FindTutorialLesson())
	trailsGroup.PUT("/tutorials/:id/lessons/:identifier", tc.UpdateTutorialLesson(), middlewares.AdminGuard())
	trailsGroup.DELETE("/tutorials/:id/lessons/:identifier", tc.DeleteTutorialLesson(), middlewares.AdminGuard())

	// V1 API (Legacy) - Keep existing trail routes for backward compatibility
	trailGroup := legacyGroup.Group("content/trail/", middlewares.AuthGuard())
	stockGroup := legacyGroup.Group("content/trail/tutorial/", middlewares.AuthGuard())

	// CRUD
	trailGroup.GET(":id/", tc.FindRegularTrail())
	trailGroup.GET("findAll/", tc.FindAllRegularTrails())
	trailGroup.POST("findByIdentifier/", tc.LegacyFindRegularTrailByIdentifier())

	// Tutorial
	stockGroup.GET(":id/", tc.FindTutorial())
	stockGroup.POST("findByTicker/", tc.FindTutorialByTicker())

	// Lesson CRUD
	trailGroup.GET(":trailId/lesson/:id/", tc.FindRegularLesson())
}

// CRUD
func (tc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get user's contracts
		userContracts, err := tc.ContractService.FindByCustomer(ctx, user.ID, true)
		if err != nil {
			return err
		}

		// Extract contract IDs
		contractIDs := make([]string, 0, len(userContracts))
		for _, contract := range userContracts {
			contractIDs = append(contractIDs, contract.ID)
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trail, err := tc.Service.Find(ctx, c.Param("id"))
		if err != nil {
			return err
		}

		if !trail.HasAccess(userClassification, contractIDs) {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get user's contracts
		userContracts, err := tc.ContractService.FindByCustomer(ctx, user.ID, true)
		if err != nil {
			return err
		}

		// Extract contract IDs
		contractIDs := make([]string, 0, len(userContracts))
		for _, contract := range userContracts {
			contractIDs = append(contractIDs, contract.ID)
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trails, err := tc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		// Sanitize trails
		sanitizedTrails := make([]*content.Trail, 0, len(trails))
		for _, trail := range trails {
			if !trail.HasAccess(userClassification, contractIDs) {
				continue
			}
			sanitizedTrails = append(sanitizedTrails, trail.Sanitize())
		}

		return c.JSON(http.StatusOK, sanitizedTrails)
	}
}

func (tc *controller) FindByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get user's contracts
		userContracts, err := tc.ContractService.FindByCustomer(ctx, user.ID, true)
		if err != nil {
			return err
		}

		// Extract contract IDs
		contractIDs := make([]string, 0, len(userContracts))
		for _, contract := range userContracts {
			contractIDs = append(contractIDs, contract.ID)
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		trail, err := tc.Service.FindByIdentifier(ctx, c.Param("identifier"))
		if err != nil {
			return err
		}

		if !trail.HasAccess(userClassification, contractIDs) {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

func (tc *controller) FindCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get user's contracts
		userContracts, err := tc.ContractService.FindByCustomer(ctx, user.ID, true)
		if err != nil {
			return err
		}

		// Extract contract IDs
		contractIDs := make([]string, 0, len(userContracts))
		for _, contract := range userContracts {
			contractIDs = append(contractIDs, contract.ID)
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		cardData, err := tc.Service.FindCardData(ctx, c.Param("id"))
		if err != nil {
			return err
		}

		if !cardData.HasAccess(userClassification, contractIDs) {
			return errors.New(errors.Controller, "access denied", errors.Forbidden, nil)
		}

		return c.JSON(http.StatusOK, cardData)
	}
}

func (tc *controller) FindAllCardData() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get user details from user service
		user, err := tc.UserService.Find(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get user's contracts
		userContracts, err := tc.ContractService.FindByCustomer(ctx, user.ID, true)
		if err != nil {
			return err
		}

		// Extract contract IDs
		contractIDs := make([]string, 0, len(userContracts))
		for _, contract := range userContracts {
			contractIDs = append(contractIDs, contract.ID)
		}

		userClassification := user.Classification
		if userClassification == "" {
			// Fallback to default if not set
			userClassification = "standard"
		}

		allCardData, err := tc.Service.FindAllCardData(ctx)
		if err != nil {
			return err
		}

		if allCardData == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		// Sanitize trails
		sanitizedCardData := make([]*content.TrailCard, 0, len(allCardData))
		for _, cardData := range allCardData {
			if !cardData.HasAccess(userClassification, contractIDs) {
				continue
			}
			sanitizedCardData = append(sanitizedCardData, cardData)
		}

		return c.JSON(http.StatusOK, sanitizedCardData)
	}
}

func (tc *controller) LegacyFindRegularTrailByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var body map[string]interface{}
		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		identifierParam := strings.ToLower(strings.TrimSpace(body["identifier"].(string)))
		trail, err := tc.Service.FindRegularTrailByIdentifier(ctx, identifierParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, trail.Sanitize())
	}
}

// Helper
func sanitizeString(str string) string {
	return strings.ToLower(strings.TrimSpace(str))
}
