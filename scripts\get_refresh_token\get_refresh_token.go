package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

func main() {
	// Make sure you have set these environment variables before running the program
	clientID := os.Getenv("GMAIL_CLIENT_ID")
	clientSecret := os.Getenv("GMAIL_CLIENT_SECRET")

	if clientID == "" || clientSecret == "" {
		log.Fatal("FATAL: Make sure GMAIL_CLIENT_ID and GMAIL_CLIENT_SECRET environment variables are set.")
	}

	config := &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Endpoint:     google.Endpoint,
		// <<< CHANGE #1: ADD THIS LINE. It must match what you put in the Google Cloud Console.
		RedirectURL: "http://localhost",
		Scopes:      []string{"https://www.googleapis.com/auth/gmail.send"},
	}

	// 1. Get the Authorization URL
	authURL := config.AuthCodeURL("state-token", oauth2.AccessTypeOffline)
	fmt.Println("------------------------------------------------------------------")
	fmt.Println("STEP 1: Authorize this application")
	fmt.Println("------------------------------------------------------------------")
	fmt.Printf("Go to the following link in your browser (logged in as %s):\n\n", "<EMAIL>")
	fmt.Println(authURL)
	fmt.Println("------------------------------------------------------------------")

	// 2. Get the Authorization Code from the user
	fmt.Println("\nAfter authorizing, you will be redirected to a page.")
	fmt.Println("It will say 'This site can’t be reached', which is an EXPECTED part of this process.")
	fmt.Println("Copy the 'code' parameter from the URL in your browser's address bar.")
	fmt.Print("Paste the authorization code here and press Enter: ")

	var authCode string
	if _, err := fmt.Scan(&authCode); err != nil {
		log.Fatalf("Unable to read authorization code: %v", err)
	}

	// 3. Exchange the Authorization Code for a Token
	tok, err := config.Exchange(context.Background(), authCode)
	if err != nil {
		log.Fatalf("Unable to retrieve token from web: %v", err)
	}

	// 4. Print the Refresh Token
	fmt.Println("\n------------------------------------------------------------------")
	fmt.Println("SUCCESS! Here is your Refresh Token. Store it securely!")
	fmt.Println("------------------------------------------------------------------")
	fmt.Printf("Your Refresh Token is:\n\n%s\n\n", tok.RefreshToken)
	fmt.Println("Set this value as your GMAIL_REFRESH_TOKEN environment variable.")
	fmt.Println("------------------------------------------------------------------")
}
